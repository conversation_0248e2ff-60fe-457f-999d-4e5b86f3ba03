<?php
/**
 * Test Schema Output for Petting Zoo Pages
 * 
 * This file can be used to test the schema output without loading the full page.
 * Access via: /wp-content/themes/generatepress-child/test-schema.php?post_id=123
 */

// Include WordPress
require_once('../../../wp-load.php');

// Get post ID from URL parameter
$post_id = isset($_GET['post_id']) ? intval($_GET['post_id']) : 0;

if (!$post_id) {
    // Get the first petting zoo post for testing
    $posts = get_posts(array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => 1,
        'post_status' => 'publish'
    ));
    
    if (!empty($posts)) {
        $post_id = $posts[0]->ID;
    } else {
        die('No petting zoo posts found. Please create a petting zoo post first.');
    }
}

// Verify it's a petting zoo post
$post = get_post($post_id);
if (!$post || $post->post_type !== 'petting_zoo') {
    die('Invalid petting zoo post ID: ' . $post_id);
}

// Set up global post data
global $post;
setup_postdata($post);

// Include the schema generation function from single-petting_zoo.php
// We'll recreate it here for testing
function generate_petting_zoo_schema_test($post_id) {
    // Get all necessary data
    $title = get_the_title($post_id);
    $description = get_the_content($post_id);
    $description = wp_strip_all_tags($description);
    $description = wp_trim_words($description, 30);
    
    $address = get_post_meta($post_id, '_petting_zoo_address', true);
    $phone = get_post_meta($post_id, '_petting_zoo_phone', true);
    $website = get_post_meta($post_id, '_petting_zoo_website', true);
    $hours = get_post_meta($post_id, '_petting_zoo_hours', true);
    $latitude = get_post_meta($post_id, '_petting_zoo_latitude', true);
    $longitude = get_post_meta($post_id, '_petting_zoo_longitude', true);
    $google_maps_url = get_post_meta($post_id, '_petting_zoo_google_maps_url', true);
    $youtube_url = get_post_meta($post_id, '_petting_zoo_youtube', true);
    $rating = get_post_meta($post_id, '_petting_zoo_rating', true);
    $rating_count = get_post_meta($post_id, '_petting_zoo_rating_count', true);
    $business_status = get_post_meta($post_id, '_petting_zoo_business_status', true);
    
    // Get images
    $pics_data = get_post_meta($post_id, '_petting_zoo_pics', true);
    $pics = $pics_data ? json_decode($pics_data, true) : array();
    
    // Get location data for breadcrumbs
    $locations = get_the_terms($post_id, 'location');
    $state = null;
    $city = null;
    
    if ($locations && !is_wp_error($locations)) {
        foreach ($locations as $location) {
            if ($location->parent == 0) {
                $state = $location;
            } else {
                $city = $location;
            }
        }
    }
    
    // Parse address for structured data
    $street_address = '';
    $locality = '';
    $region = '';
    $postal_code = '';
    
    if ($address) {
        // Try to parse address components
        $address_parts = explode(',', $address);
        if (count($address_parts) >= 3) {
            $street_address = trim($address_parts[0]);
            $locality = trim($address_parts[1]);
            $region_postal = trim($address_parts[2]);
            
            // Extract state and postal code
            if (preg_match('/^(.+?)\s+(\d{5}(?:-\d{4})?)$/', $region_postal, $matches)) {
                $region = trim($matches[1]);
                $postal_code = trim($matches[2]);
            } else {
                $region = $region_postal;
            }
        } else {
            $street_address = $address;
            if ($city) $locality = $city->name;
            if ($state) $region = $state->name;
        }
    }
    
    // Get site URL
    $site_url = home_url();
    $zoo_url = get_permalink($post_id);
    
    // Build image array
    $images = array();
    if (!empty($pics)) {
        foreach ($pics as $pic) {
            if (isset($pic['url']) && $pic['url']) {
                $images[] = $pic['url'];
            }
        }
    }
    
    // Fallback image if no pics
    if (empty($images)) {
        $images[] = $site_url . '/wp-content/uploads/2025/06/pettingzoophoto (1).webp';
    }
    
    return array(
        'site_url' => $site_url,
        'zoo_url' => $zoo_url,
        'title' => $title,
        'description' => $description,
        'images' => $images,
        'address' => $address,
        'street_address' => $street_address,
        'locality' => $locality,
        'region' => $region,
        'postal_code' => $postal_code,
        'phone' => $phone,
        'website' => $website,
        'latitude' => $latitude,
        'longitude' => $longitude,
        'google_maps_url' => $google_maps_url,
        'youtube_url' => $youtube_url,
        'rating' => $rating,
        'rating_count' => $rating_count,
        'business_status' => $business_status,
        'state' => $state,
        'city' => $city
    );
}

$schema_data = generate_petting_zoo_schema_test($post_id);

// Output HTML for testing
?>
<!DOCTYPE html>
<html>
<head>
    <title>Schema Test for <?php echo esc_html($schema_data['title']); ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .schema-box { background: #f5f5f5; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .data-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background-color: #f2f2f2; }
        pre { background: #f8f8f8; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .validation-links { margin: 20px 0; }
        .validation-links a { display: inline-block; margin: 5px 10px 5px 0; padding: 10px 15px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; }
        .validation-links a:hover { background: #005a87; }
    </style>
</head>
<body>
    <h1>Schema Test Results</h1>
    <h2>Post: <?php echo esc_html($schema_data['title']); ?> (ID: <?php echo $post_id; ?>)</h2>
    
    <div class="validation-links">
        <h3>Validation Tools:</h3>
        <a href="https://search.google.com/test/rich-results?url=<?php echo urlencode($schema_data['zoo_url']); ?>" target="_blank">Google Rich Results Test</a>
        <a href="https://validator.schema.org/" target="_blank">Schema.org Validator</a>
        <a href="https://developers.facebook.com/tools/debug/?q=<?php echo urlencode($schema_data['zoo_url']); ?>" target="_blank">Facebook Debugger</a>
    </div>
    
    <div class="schema-box">
        <h3>Extracted Data:</h3>
        <table class="data-table">
            <tr><th>Field</th><th>Value</th></tr>
            <tr><td>Title</td><td><?php echo esc_html($schema_data['title']); ?></td></tr>
            <tr><td>Description</td><td><?php echo esc_html($schema_data['description']); ?></td></tr>
            <tr><td>Address</td><td><?php echo esc_html($schema_data['address']); ?></td></tr>
            <tr><td>Phone</td><td><?php echo esc_html($schema_data['phone']); ?></td></tr>
            <tr><td>Website</td><td><?php echo esc_html($schema_data['website']); ?></td></tr>
            <tr><td>Coordinates</td><td><?php echo esc_html($schema_data['latitude'] . ', ' . $schema_data['longitude']); ?></td></tr>
            <tr><td>Rating</td><td><?php echo esc_html($schema_data['rating'] . ' (' . $schema_data['rating_count'] . ' reviews)'); ?></td></tr>
            <tr><td>Business Status</td><td><?php echo esc_html($schema_data['business_status']); ?></td></tr>
            <tr><td>Images Count</td><td><?php echo count($schema_data['images']); ?></td></tr>
            <tr><td>YouTube URL</td><td><?php echo esc_html($schema_data['youtube_url']); ?></td></tr>
            <tr><td>State</td><td><?php echo $schema_data['state'] ? esc_html($schema_data['state']->name) : 'None'; ?></td></tr>
            <tr><td>City</td><td><?php echo $schema_data['city'] ? esc_html($schema_data['city']->name) : 'None'; ?></td></tr>
        </table>
    </div>
    
    <div class="schema-box">
        <h3>Generated Schema (copy this to test in validators):</h3>
        <p><strong>Note:</strong> This is a simplified version. The actual page includes additional FAQ and breadcrumb schemas.</p>
        <pre><?php
        // Generate basic zoo schema for testing
        $test_schema = array(
            '@context' => 'https://schema.org',
            '@type' => array('Zoo', 'TouristAttraction'),
            'name' => $schema_data['title'],
            'description' => $schema_data['description'],
            'url' => $schema_data['zoo_url'],
            'image' => $schema_data['images'],
            'address' => array(
                '@type' => 'PostalAddress',
                'streetAddress' => $schema_data['street_address'],
                'addressLocality' => $schema_data['locality'],
                'addressRegion' => $schema_data['region'],
                'postalCode' => $schema_data['postal_code'],
                'addressCountry' => 'US'
            ),
            'telephone' => $schema_data['phone'],
            'geo' => array(
                '@type' => 'GeoCoordinates',
                'latitude' => floatval($schema_data['latitude']),
                'longitude' => floatval($schema_data['longitude'])
            ),
            'publicAccess' => true,
            'isAccessibleForFree' => false,
            'priceRange' => '$$'
        );
        
        if ($schema_data['rating'] && $schema_data['rating_count']) {
            $test_schema['aggregateRating'] = array(
                '@type' => 'AggregateRating',
                'ratingValue' => floatval($schema_data['rating']),
                'reviewCount' => intval($schema_data['rating_count']),
                'bestRating' => 5,
                'worstRating' => 1
            );
        }
        
        echo wp_json_encode($test_schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
        ?></pre>
    </div>
    
    <p><a href="<?php echo $schema_data['zoo_url']; ?>" target="_blank">View actual page →</a></p>
    
</body>
</html>

<?php
wp_reset_postdata();
?>
