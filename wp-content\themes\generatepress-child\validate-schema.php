<?php
/**
 * Schema Validation Script for Petting Zoo Pages
 * 
 * This script checks all petting zoo pages and validates their schema output.
 * Access via: /wp-content/themes/generatepress-child/validate-schema.php
 */

// Include WordPress
require_once('../../../wp-load.php');

// Check if user is admin
if (!current_user_can('manage_options')) {
    die('Access denied. Admin privileges required.');
}

// Get all petting zoo posts
$petting_zoos = get_posts(array(
    'post_type' => 'petting_zoo',
    'posts_per_page' => -1,
    'post_status' => 'publish'
));

?>
<!DOCTYPE html>
<html>
<head>
    <title>Petting Zoo Schema Validation Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #0073aa; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .zoo-item { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .zoo-item.complete { border-color: #46b450; background: #f7fcf0; }
        .zoo-item.partial { border-color: #ffb900; background: #fffbf0; }
        .zoo-item.incomplete { border-color: #dc3232; background: #fdf0f0; }
        .status { font-weight: bold; padding: 5px 10px; border-radius: 3px; color: white; }
        .status.complete { background: #46b450; }
        .status.partial { background: #ffb900; }
        .status.incomplete { background: #dc3232; }
        .data-grid { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin: 10px 0; }
        .data-item { background: #f9f9f9; padding: 8px; border-radius: 3px; font-size: 0.9em; }
        .actions { margin: 10px 0; }
        .actions a { display: inline-block; margin: 5px 10px 5px 0; padding: 8px 12px; background: #0073aa; color: white; text-decoration: none; border-radius: 3px; font-size: 0.9em; }
        .actions a:hover { background: #005a87; }
        .summary { background: #f0f0f1; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
        .summary-stats { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; text-align: center; }
        .stat-box { background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd; }
        .stat-number { font-size: 2em; font-weight: bold; color: #0073aa; }
        .missing-data { color: #dc3232; font-style: italic; }
        .has-data { color: #46b450; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Petting Zoo Schema Validation Report</h1>
        <p>Comprehensive analysis of structured data implementation across all petting zoo pages.</p>
    </div>

    <?php
    // Initialize counters
    $total_zoos = count($petting_zoos);
    $complete_count = 0;
    $partial_count = 0;
    $incomplete_count = 0;
    $total_missing_data = 0;

    // Analyze each zoo
    $zoo_analysis = array();
    
    foreach ($petting_zoos as $zoo) {
        $analysis = array(
            'id' => $zoo->ID,
            'title' => $zoo->post_title,
            'url' => get_permalink($zoo->ID),
            'data' => array(),
            'missing' => array(),
            'score' => 0
        );
        
        // Check essential data fields
        $fields_to_check = array(
            'address' => get_post_meta($zoo->ID, '_petting_zoo_address', true),
            'phone' => get_post_meta($zoo->ID, '_petting_zoo_phone', true),
            'website' => get_post_meta($zoo->ID, '_petting_zoo_website', true),
            'hours' => get_post_meta($zoo->ID, '_petting_zoo_hours', true),
            'latitude' => get_post_meta($zoo->ID, '_petting_zoo_latitude', true),
            'longitude' => get_post_meta($zoo->ID, '_petting_zoo_longitude', true),
            'rating' => get_post_meta($zoo->ID, '_petting_zoo_rating', true),
            'rating_count' => get_post_meta($zoo->ID, '_petting_zoo_rating_count', true),
            'business_status' => get_post_meta($zoo->ID, '_petting_zoo_business_status', true),
            'pics' => get_post_meta($zoo->ID, '_petting_zoo_pics', true),
            'youtube' => get_post_meta($zoo->ID, '_petting_zoo_youtube', true),
            'faq' => get_post_meta($zoo->ID, '_petting_zoo_faq', true)
        );
        
        // Check taxonomy terms
        $locations = get_the_terms($zoo->ID, 'location');
        $features = get_the_terms($zoo->ID, 'features');
        $animals = get_the_terms($zoo->ID, 'animal_type');
        
        $fields_to_check['location_terms'] = ($locations && !is_wp_error($locations)) ? count($locations) : 0;
        $fields_to_check['feature_terms'] = ($features && !is_wp_error($features)) ? count($features) : 0;
        $fields_to_check['animal_terms'] = ($animals && !is_wp_error($animals)) ? count($animals) : 0;
        
        // Analyze data completeness
        foreach ($fields_to_check as $field => $value) {
            if (!empty($value)) {
                $analysis['data'][$field] = $value;
                $analysis['score']++;
            } else {
                $analysis['missing'][] = $field;
                $total_missing_data++;
            }
        }
        
        // Determine status
        $total_fields = count($fields_to_check);
        $completion_rate = $analysis['score'] / $total_fields;
        
        if ($completion_rate >= 0.8) {
            $analysis['status'] = 'complete';
            $complete_count++;
        } elseif ($completion_rate >= 0.5) {
            $analysis['status'] = 'partial';
            $partial_count++;
        } else {
            $analysis['status'] = 'incomplete';
            $incomplete_count++;
        }
        
        $zoo_analysis[] = $analysis;
    }
    ?>

    <div class="summary">
        <h2>📊 Summary Statistics</h2>
        <div class="summary-stats">
            <div class="stat-box">
                <div class="stat-number"><?php echo $total_zoos; ?></div>
                <div>Total Zoos</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" style="color: #46b450;"><?php echo $complete_count; ?></div>
                <div>Complete (80%+)</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" style="color: #ffb900;"><?php echo $partial_count; ?></div>
                <div>Partial (50-79%)</div>
            </div>
            <div class="stat-box">
                <div class="stat-number" style="color: #dc3232;"><?php echo $incomplete_count; ?></div>
                <div>Incomplete (<50%)</div>
            </div>
        </div>
    </div>

    <h2>🏢 Individual Zoo Analysis</h2>
    
    <?php foreach ($zoo_analysis as $zoo): ?>
        <div class="zoo-item <?php echo $zoo['status']; ?>">
            <h3>
                <?php echo esc_html($zoo['title']); ?>
                <span class="status <?php echo $zoo['status']; ?>">
                    <?php echo strtoupper($zoo['status']); ?> 
                    (<?php echo $zoo['score']; ?>/<?php echo count($fields_to_check); ?>)
                </span>
            </h3>
            
            <div class="data-grid">
                <div class="data-item">
                    <strong>Address:</strong> 
                    <?php echo isset($zoo['data']['address']) ? '<span class="has-data">✓ Present</span>' : '<span class="missing-data">✗ Missing</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Phone:</strong> 
                    <?php echo isset($zoo['data']['phone']) ? '<span class="has-data">✓ Present</span>' : '<span class="missing-data">✗ Missing</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Coordinates:</strong> 
                    <?php echo (isset($zoo['data']['latitude']) && isset($zoo['data']['longitude'])) ? '<span class="has-data">✓ Present</span>' : '<span class="missing-data">✗ Missing</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Rating:</strong> 
                    <?php echo (isset($zoo['data']['rating']) && isset($zoo['data']['rating_count'])) ? '<span class="has-data">✓ Present</span>' : '<span class="missing-data">✗ Missing</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Images:</strong> 
                    <?php echo isset($zoo['data']['pics']) ? '<span class="has-data">✓ Present</span>' : '<span class="missing-data">✗ Missing</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Video:</strong> 
                    <?php echo isset($zoo['data']['youtube']) ? '<span class="has-data">✓ Present</span>' : '<span class="missing-data">✗ Missing</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Location Terms:</strong> 
                    <?php echo isset($zoo['data']['location_terms']) && $zoo['data']['location_terms'] > 0 ? '<span class="has-data">✓ ' . $zoo['data']['location_terms'] . ' terms</span>' : '<span class="missing-data">✗ No terms</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Features:</strong> 
                    <?php echo isset($zoo['data']['feature_terms']) && $zoo['data']['feature_terms'] > 0 ? '<span class="has-data">✓ ' . $zoo['data']['feature_terms'] . ' features</span>' : '<span class="missing-data">✗ No features</span>'; ?>
                </div>
                <div class="data-item">
                    <strong>Animals:</strong> 
                    <?php echo isset($zoo['data']['animal_terms']) && $zoo['data']['animal_terms'] > 0 ? '<span class="has-data">✓ ' . $zoo['data']['animal_terms'] . ' types</span>' : '<span class="missing-data">✗ No animals</span>'; ?>
                </div>
            </div>
            
            <?php if (!empty($zoo['missing'])): ?>
                <div style="margin-top: 10px;">
                    <strong>Missing Data:</strong> 
                    <span class="missing-data"><?php echo implode(', ', $zoo['missing']); ?></span>
                </div>
            <?php endif; ?>
            
            <div class="actions">
                <a href="<?php echo $zoo['url']; ?>" target="_blank">View Page</a>
                <a href="<?php echo admin_url('post.php?post=' . $zoo['id'] . '&action=edit'); ?>" target="_blank">Edit Post</a>
                <a href="test-schema.php?post_id=<?php echo $zoo['id']; ?>" target="_blank">Test Schema</a>
                <a href="https://search.google.com/test/rich-results?url=<?php echo urlencode($zoo['url']); ?>" target="_blank">Google Test</a>
            </div>
        </div>
    <?php endforeach; ?>

    <div class="summary" style="margin-top: 30px;">
        <h2>🔧 Recommendations</h2>
        <ul>
            <li><strong>Priority 1:</strong> Complete missing address and coordinate data for incomplete zoos</li>
            <li><strong>Priority 2:</strong> Add location taxonomy terms for proper breadcrumb generation</li>
            <li><strong>Priority 3:</strong> Import rating and review data to enhance rich snippets</li>
            <li><strong>Priority 4:</strong> Add feature and animal taxonomy terms for better categorization</li>
            <li><strong>Testing:</strong> Use Google Rich Results Test to validate schema implementation</li>
            <li><strong>Monitoring:</strong> Check Google Search Console for structured data errors</li>
        </ul>
    </div>

    <div class="summary">
        <h2>📋 Next Steps</h2>
        <ol>
            <li>Run the petting zoo importer to populate missing data fields</li>
            <li>Test schema output using the validation tools provided</li>
            <li>Monitor Google Search Console for rich snippet performance</li>
            <li>Update any zoos with incomplete data using the edit links above</li>
        </ol>
    </div>

</body>
</html>
