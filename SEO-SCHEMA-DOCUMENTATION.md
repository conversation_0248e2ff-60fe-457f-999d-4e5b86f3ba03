# SEO Schema Implementation for Petting Zoo Pages

## Overview

This implementation adds comprehensive JSON-LD structured data to all petting zoo pages, significantly improving SEO visibility and search engine understanding of the content. The schema includes multiple schema.org types to cover all aspects of a petting zoo business.

## Schema Types Implemented

### 1. Zoo & TouristAttraction Schema
- **Primary Type**: `Zoo` and `TouristAttraction`
- **Purpose**: Identifies the business as a zoo and tourist destination
- **Key Properties**:
  - Business name, description, and URL
  - Complete address with postal code
  - Geographic coordinates (latitude/longitude)
  - Contact information (phone, website)
  - Opening hours in structured format
  - Business status (operational/closed)
  - Accessibility features and amenities
  - Target audience (family-friendly)
  - Price range indication

### 2. FAQPage Schema
- **Purpose**: Enhances search results with FAQ rich snippets
- **Sources**: 
  - Dynamic FAQs from JSON data if available
  - Fallback to default petting zoo FAQs
- **Benefits**: Can appear as expandable FAQ sections in search results

### 3. VideoObject Schema
- **Purpose**: Promotes YouTube videos in search results
- **Triggers**: When a YouTube URL is present in the zoo data
- **Properties**:
  - Video title and description
  - Thumbnail URL (auto-generated from YouTube)
  - Upload date
  - Content and embed URLs

### 4. BreadcrumbList Schema
- **Purpose**: Shows navigation path in search results
- **Structure**: Home → State → City → Zoo Name
- **Benefits**: Improves user navigation and site structure understanding

## Data Sources

The schema automatically extracts data from WordPress meta fields:

### Core Business Information
- `_petting_zoo_address` → Parsed into structured address components
- `_petting_zoo_phone` → Business telephone
- `_petting_zoo_website` → Official website URL
- `_petting_zoo_hours` → Parsed into OpeningHoursSpecification
- `_petting_zoo_latitude` / `_petting_zoo_longitude` → Geographic coordinates
- `_petting_zoo_google_maps_url` → Maps link
- `_petting_zoo_business_status` → Operational status



### Media Content
- `_petting_zoo_pics` → JSON array of images
- `_petting_zoo_youtube` → YouTube video URL
- Fallback to placeholder images if no pics available

### FAQ Content
- `_petting_zoo_faq` → JSON array of questions and answers
- Fallback to default petting zoo FAQs

### Taxonomy Integration
- `location` taxonomy → Used for breadcrumbs and address parsing
- `features` taxonomy → Converted to amenityFeature properties

## Implementation Details

### Address Parsing
The system intelligently parses address strings into structured components:
```
"123 Main St, Albany, NY 12345" becomes:
- streetAddress: "123 Main St"
- addressLocality: "Albany" 
- addressRegion: "NY"
- postalCode: "12345"
- addressCountry: "US"
```

### Opening Hours Parsing
Converts natural language hours into structured format:
```
"Monday: 10:00 AM - 5:00 PM" becomes:
{
  "@type": "OpeningHoursSpecification",
  "dayOfWeek": "Monday",
  "opens": "10:00",
  "closes": "17:00"
}
```

### Image Handling
- Prioritizes actual zoo photos from JSON data
- Falls back to placeholder images for consistent presentation
- Supports up to 8 images per zoo
- Automatically handles URL conversion

## SEO Benefits

### Rich Snippets
- **Business Information**: Name, hours, phone in search results
- **FAQ Snippets**: Expandable Q&A sections
- **Breadcrumb Navigation**: Clear site hierarchy
- **Video Previews**: YouTube content promotion

### Local SEO
- **Geographic Coordinates**: Precise location data
- **Address Structure**: Proper local business markup
- **Opening Hours**: Helps with "open now" searches
- **Phone Numbers**: Click-to-call functionality

### Content Understanding
- **Business Type**: Clear zoo/tourist attraction classification
- **Target Audience**: Family-friendly designation
- **Accessibility**: Wheelchair access and amenity information
- **Price Range**: Budget expectation setting

## Testing and Validation

### Built-in Testing Tools
1. **Admin Bar Link**: "🔍 Test Schema" appears on petting zoo pages
2. **Admin Notice**: Confirmation that schema is active
3. **Test File**: `/wp-content/themes/generatepress-child/test-schema.php`

### External Validation Tools
1. **Google Rich Results Test**: https://search.google.com/test/rich-results
2. **Schema.org Validator**: https://validator.schema.org/
3. **Facebook Debugger**: https://developers.facebook.com/tools/debug/

### Testing Process
1. Visit any petting zoo page
2. View page source and search for `application/ld+json`
3. Copy the JSON-LD content
4. Paste into validation tools
5. Check for errors and warnings

## Maintenance

### Automatic Updates
- Schema updates automatically when post data changes
- No manual intervention required for most updates
- Handles missing data gracefully with fallbacks

### Monitoring
- Use Google Search Console to monitor rich snippet performance
- Check for schema errors in GSC's Enhancement reports
- Monitor click-through rates for pages with rich snippets

## Customization

### Adding New Schema Types
To add additional schema types, modify the `$schema_graph['@graph']` array in `single-petting_zoo.php`.

### Modifying Existing Schema
Update the relevant sections in the schema generation function. Common modifications:
- Adding new business properties
- Updating default FAQ content
- Modifying amenity features
- Changing price range or audience targeting

### Site-Specific Adjustments
- Update the site URL in schema generation if moving from local development
- Modify default images and fallback content
- Adjust opening hours parsing for different formats

## Performance Impact

- **Minimal**: JSON-LD is lightweight and doesn't affect page rendering
- **Cached**: Schema generation uses existing meta queries
- **Efficient**: Single database query per data type
- **Non-blocking**: Doesn't impact page load speed

## Compliance

- **Schema.org Standards**: Follows official schema.org specifications
- **Google Guidelines**: Meets Google's structured data requirements
- **Accessibility**: Includes accessibility-related properties
- **Privacy**: No personal data collection or tracking
