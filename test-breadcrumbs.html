<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Breadcrumbs Test - Petting Zoo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }

        /* Hero Section Styling */
        .zoo-hero-section {
            position: relative;
            width: 100vw;
            margin-left: calc(-50vw + 50%);
            margin-right: calc(-50vw + 50%);
            height: 500px;
            background-image: url('https://via.placeholder.com/1400x500/2f6130/ffffff?text=Petting+Zoo+Hero+Image');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .zoo-hero-section .hero-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(47, 97, 48, 0.6) 0%, rgba(30, 63, 32, 0.7) 100%);
            z-index: 1;
        }

        .zoo-hero-section .hero-content-wrapper {
            position: relative;
            z-index: 2;
            width: 100%;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .zoo-hero-section .hero-content {
            text-align: center;
            color: white;
        }

        /* Breadcrumbs Styling */
        .zoo-breadcrumbs {
            margin-bottom: 1.5rem;
        }

        .breadcrumb-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            align-items: center;
            list-style: none;
            margin: 0;
            padding: 0;
            gap: 0.5rem;
        }

        .breadcrumb-item {
            display: flex;
            align-items: center;
            font-size: 0.95rem;
            color: rgba(255, 255, 255, 0.9);
        }

        .breadcrumb-item a {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(5px);
        }

        .breadcrumb-item a:hover {
            color: white;
            background: rgba(255, 255, 255, 0.2);
            text-decoration: none;
        }

        .breadcrumb-separator {
            margin: 0 0.25rem;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 300;
        }

        .breadcrumb-item.current span {
            color: white;
            font-weight: 500;
        }

        .zoo-hero-section .zoo-title {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            color: white;
        }

        .zoo-hero-section .zoo-address {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.95;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        /* Mobile responsive styles */
        @media (max-width: 768px) {
            .zoo-hero-section {
                height: 400px;
            }

            .zoo-hero-section .hero-content-wrapper {
                padding: 0 1rem;
            }

            .zoo-breadcrumbs {
                margin-bottom: 1rem;
            }

            .breadcrumb-list {
                font-size: 0.85rem;
                gap: 0.25rem;
            }

            .breadcrumb-item a {
                padding: 0.2rem 0.4rem;
                font-size: 0.85rem;
            }

            .breadcrumb-separator {
                margin: 0 0.15rem;
            }

            .zoo-hero-section .zoo-title {
                font-size: 2.5rem;
            }

            .zoo-hero-section .zoo-address {
                font-size: 1.1rem;
            }
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .content {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="zoo-hero-section">
        <div class="hero-overlay"></div>
        <div class="hero-content-wrapper">
            <div class="hero-content">
                <!-- Breadcrumbs -->
                <nav class="zoo-breadcrumbs" aria-label="Breadcrumb">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="/">🏠 Home</a>
                        </li>
                        <li class="breadcrumb-item">
                            <span class="breadcrumb-separator">›</span>
                            <a href="/location/new-york/">New York</a>
                        </li>
                        <li class="breadcrumb-item">
                            <span class="breadcrumb-separator">›</span>
                            <a href="/location/albany/">Albany</a>
                        </li>
                        <li class="breadcrumb-item current">
                            <span class="breadcrumb-separator">›</span>
                            <span>Bailiwick Animal Park and Riding Stables</span>
                        </li>
                    </ol>
                </nav>

                <h1 class="zoo-title">Bailiwick Animal Park and Riding Stables</h1>

                <div class="zoo-address">
                    <span class="address-icon">📍</span>
                    123 Farm Road, Albany, NY 12345
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="content">
            <h2>Breadcrumbs Test</h2>
            <p>This page demonstrates how the breadcrumbs should look on petting zoo pages. The breadcrumbs are displayed in the hero section above the title and provide navigation context:</p>
            
            <ul>
                <li><strong>Home</strong> - Links to the homepage</li>
                <li><strong>State</strong> - Links to the state page (e.g., "New York")</li>
                <li><strong>City</strong> - Links to the city page (e.g., "Albany")</li>
                <li><strong>Current Zoo</strong> - Shows the current zoo name (not linked)</li>
            </ul>

            <h3>Features:</h3>
            <ul>
                <li>Responsive design that works on mobile and desktop</li>
                <li>Semi-transparent background with blur effect</li>
                <li>Hover effects on links</li>
                <li>Proper semantic HTML with aria-label for accessibility</li>
                <li>Consistent with the site's color scheme</li>
            </ul>

            <p>The breadcrumbs are also included in the structured data (JSON-LD schema) for SEO benefits.</p>
        </div>
    </div>
</body>
</html>
