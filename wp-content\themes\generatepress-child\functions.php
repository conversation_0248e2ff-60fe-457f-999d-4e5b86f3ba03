<?php
/**
 * GeneratePress Child Theme Functions
 * Petting Zoo Directory Website
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Enqueue parent and child theme styles with enhanced design system
 */
function generatepress_child_enqueue_styles() {
    // Enqueue Google Fonts for enhanced typography
    wp_enqueue_style('petting-zoo-fonts',
        'https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&family=Inter:wght@400;500;600&display=swap',
        array(),
        null
    );

    // Enqueue parent theme style
    wp_enqueue_style('generatepress-parent-style', get_template_directory_uri() . '/style.css');

    // Enqueue child theme style with proper dependencies
    wp_enqueue_style('generatepress-child-style',
        get_stylesheet_directory_uri() . '/style.css',
        array('generatepress-parent-style', 'petting-zoo-fonts'),
        wp_get_theme()->get('Version')
    );

    // Enqueue custom JavaScript with enhanced functionality
    wp_enqueue_script('petting-zoo-scripts',
        get_stylesheet_directory_uri() . '/assets/js/petting-zoo.js',
        array('jquery'),
        wp_get_theme()->get('Version'),
        true
    );

    // Localize script for AJAX with additional data
    wp_localize_script('petting-zoo-scripts', 'pettingZooAjax', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('petting_zoo_nonce'),
        'loading_text' => __('Loading...', 'generatepress-child'),
        'error_text' => __('Something went wrong. Please try again.', 'generatepress-child')
    ));
}
add_action('wp_enqueue_scripts', 'generatepress_child_enqueue_styles');

/**
 * Register Custom Post Type: Petting Zoo
 */
function register_petting_zoo_post_type() {
    $labels = array(
        'name'                  => 'Petting Zoos',
        'singular_name'         => 'Petting Zoo',
        'menu_name'             => 'Petting Zoos',
        'name_admin_bar'        => 'Petting Zoo',
        'archives'              => 'Petting Zoo Archives',
        'attributes'            => 'Petting Zoo Attributes',
        'parent_item_colon'     => 'Parent Petting Zoo:',
        'all_items'             => 'All Petting Zoos',
        'add_new_item'          => 'Add New Petting Zoo',
        'add_new'               => 'Add New',
        'new_item'              => 'New Petting Zoo',
        'edit_item'             => 'Edit Petting Zoo',
        'update_item'           => 'Update Petting Zoo',
        'view_item'             => 'View Petting Zoo',
        'view_items'            => 'View Petting Zoos',
        'search_items'          => 'Search Petting Zoo',
        'not_found'             => 'Not found',
        'not_found_in_trash'    => 'Not found in Trash',
        'featured_image'        => 'Featured Image',
        'set_featured_image'    => 'Set featured image',
        'remove_featured_image' => 'Remove featured image',
        'use_featured_image'    => 'Use as featured image',
        'insert_into_item'      => 'Insert into petting zoo',
        'uploaded_to_this_item' => 'Uploaded to this petting zoo',
        'items_list'            => 'Petting zoos list',
        'items_list_navigation' => 'Petting zoos list navigation',
        'filter_items_list'     => 'Filter petting zoos list',
    );

    $args = array(
        'label'                 => 'Petting Zoo',
        'description'           => 'Petting Zoo Directory Listings',
        'labels'                => $labels,
        'supports'              => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'taxonomies'            => array('location', 'animal_type', 'zoo_type', 'features', 'event_type'),
        'hierarchical'          => false,
        'public'                => true,
        'show_ui'               => true,
        'show_in_menu'          => true,
        'menu_position'         => 5,
        'menu_icon'             => 'dashicons-pets',
        'show_in_admin_bar'     => true,
        'show_in_nav_menus'     => true,
        'can_export'            => true,
        'has_archive'           => true,
        'exclude_from_search'   => false,
        'publicly_queryable'    => true,
        'capability_type'       => 'post',
        'show_in_rest'          => true,
        'rewrite'               => array('slug' => 'petting-zoo'),
    );

    register_post_type('petting_zoo', $args);
}
add_action('init', 'register_petting_zoo_post_type', 0);

/**
 * Register Custom Taxonomies
 */
function register_petting_zoo_taxonomies() {
    
    // Location Taxonomy (Hierarchical: State -> City)
    register_taxonomy('location', array('petting_zoo'), array(
        'hierarchical'      => true,
        'labels'            => array(
            'name'              => 'Locations',
            'singular_name'     => 'Location',
            'search_items'      => 'Search Locations',
            'all_items'         => 'All Locations',
            'parent_item'       => 'Parent Location',
            'parent_item_colon' => 'Parent Location:',
            'edit_item'         => 'Edit Location',
            'update_item'       => 'Update Location',
            'add_new_item'      => 'Add New Location',
            'new_item_name'     => 'New Location Name',
            'menu_name'         => 'Locations',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'location'),
        'show_in_rest'      => true,
    ));

    // Animal Type Taxonomy (Non-hierarchical)
    register_taxonomy('animal_type', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Animal Types',
            'singular_name'              => 'Animal Type',
            'search_items'               => 'Search Animal Types',
            'popular_items'              => 'Popular Animal Types',
            'all_items'                  => 'All Animal Types',
            'edit_item'                  => 'Edit Animal Type',
            'update_item'                => 'Update Animal Type',
            'add_new_item'               => 'Add New Animal Type',
            'new_item_name'              => 'New Animal Type Name',
            'separate_items_with_commas' => 'Separate animal types with commas',
            'add_or_remove_items'        => 'Add or remove animal types',
            'choose_from_most_used'      => 'Choose from the most used animal types',
            'not_found'                  => 'No animal types found.',
            'menu_name'                  => 'Animal Types',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'animals'),
        'show_in_rest'      => true,
    ));

    // Zoo Type Taxonomy (Non-hierarchical)
    register_taxonomy('zoo_type', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Zoo Types',
            'singular_name'              => 'Zoo Type',
            'search_items'               => 'Search Zoo Types',
            'popular_items'              => 'Popular Zoo Types',
            'all_items'                  => 'All Zoo Types',
            'edit_item'                  => 'Edit Zoo Type',
            'update_item'                => 'Update Zoo Type',
            'add_new_item'               => 'Add New Zoo Type',
            'new_item_name'              => 'New Zoo Type Name',
            'separate_items_with_commas' => 'Separate zoo types with commas',
            'add_or_remove_items'        => 'Add or remove zoo types',
            'choose_from_most_used'      => 'Choose from the most used zoo types',
            'not_found'                  => 'No zoo types found.',
            'menu_name'                  => 'Zoo Types',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'zoo-type'),
        'show_in_rest'      => true,
    ));

    // Features Taxonomy (Non-hierarchical)
    register_taxonomy('features', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Features',
            'singular_name'              => 'Feature',
            'search_items'               => 'Search Features',
            'popular_items'              => 'Popular Features',
            'all_items'                  => 'All Features',
            'edit_item'                  => 'Edit Feature',
            'update_item'                => 'Update Feature',
            'add_new_item'               => 'Add New Feature',
            'new_item_name'              => 'New Feature Name',
            'separate_items_with_commas' => 'Separate features with commas',
            'add_or_remove_items'        => 'Add or remove features',
            'choose_from_most_used'      => 'Choose from the most used features',
            'not_found'                  => 'No features found.',
            'menu_name'                  => 'Features',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'features'),
        'show_in_rest'      => true,
    ));

    // Event Type Taxonomy (Non-hierarchical)
    register_taxonomy('event_type', array('petting_zoo'), array(
        'hierarchical'      => false,
        'labels'            => array(
            'name'                       => 'Event Types',
            'singular_name'              => 'Event Type',
            'search_items'               => 'Search Event Types',
            'popular_items'              => 'Popular Event Types',
            'all_items'                  => 'All Event Types',
            'edit_item'                  => 'Edit Event Type',
            'update_item'                => 'Update Event Type',
            'add_new_item'               => 'Add New Event Type',
            'new_item_name'              => 'New Event Type Name',
            'separate_items_with_commas' => 'Separate event types with commas',
            'add_or_remove_items'        => 'Add or remove event types',
            'choose_from_most_used'      => 'Choose from the most used event types',
            'not_found'                  => 'No event types found.',
            'menu_name'                  => 'Event Types',
        ),
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'events'),
        'show_in_rest'      => true,
    ));
}
add_action('init', 'register_petting_zoo_taxonomies', 0);

/**
 * Flush rewrite rules on theme activation
 */
function petting_zoo_flush_rewrites() {
    register_petting_zoo_post_type();
    register_petting_zoo_taxonomies();
    flush_rewrite_rules();
}
add_action('after_switch_theme', 'petting_zoo_flush_rewrites');

/**
 * Add custom meta boxes for petting zoo data
 */
function add_petting_zoo_meta_boxes() {
    add_meta_box(
        'petting_zoo_details',
        'Petting Zoo Details',
        'petting_zoo_details_callback',
        'petting_zoo',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_petting_zoo_meta_boxes');

/**
 * Meta box callback function
 */
function petting_zoo_details_callback($post) {
    wp_nonce_field('petting_zoo_meta_box', 'petting_zoo_meta_box_nonce');
    
    // Get existing values
    $address = get_post_meta($post->ID, '_petting_zoo_address', true);
    $phone = get_post_meta($post->ID, '_petting_zoo_phone', true);
    $website = get_post_meta($post->ID, '_petting_zoo_website', true);
    $hours = get_post_meta($post->ID, '_petting_zoo_hours', true);
    $admission = get_post_meta($post->ID, '_petting_zoo_admission', true);
    $latitude = get_post_meta($post->ID, '_petting_zoo_latitude', true);
    $longitude = get_post_meta($post->ID, '_petting_zoo_longitude', true);
    
    echo '<table class="form-table">';
    echo '<tr><th><label for="petting_zoo_address">Address</label></th>';
    echo '<td><textarea id="petting_zoo_address" name="petting_zoo_address" rows="3" cols="50">' . esc_textarea($address) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_phone">Phone</label></th>';
    echo '<td><input type="text" id="petting_zoo_phone" name="petting_zoo_phone" value="' . esc_attr($phone) . '" size="25" /></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_website">Website</label></th>';
    echo '<td><input type="url" id="petting_zoo_website" name="petting_zoo_website" value="' . esc_attr($website) . '" size="50" /></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_hours">Hours</label></th>';
    echo '<td><textarea id="petting_zoo_hours" name="petting_zoo_hours" rows="3" cols="50">' . esc_textarea($hours) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_admission">Admission</label></th>';
    echo '<td><textarea id="petting_zoo_admission" name="petting_zoo_admission" rows="2" cols="50">' . esc_textarea($admission) . '</textarea></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_latitude">Latitude</label></th>';
    echo '<td><input type="text" id="petting_zoo_latitude" name="petting_zoo_latitude" value="' . esc_attr($latitude) . '" size="25" /></td></tr>';
    
    echo '<tr><th><label for="petting_zoo_longitude">Longitude</label></th>';
    echo '<td><input type="text" id="petting_zoo_longitude" name="petting_zoo_longitude" value="' . esc_attr($longitude) . '" size="25" /></td></tr>';
    
    echo '</table>';
}

/**
 * Save meta box data
 */
function save_petting_zoo_meta_box_data($post_id) {
    if (!isset($_POST['petting_zoo_meta_box_nonce'])) {
        return;
    }
    
    if (!wp_verify_nonce($_POST['petting_zoo_meta_box_nonce'], 'petting_zoo_meta_box')) {
        return;
    }
    
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (isset($_POST['post_type']) && 'petting_zoo' == $_POST['post_type']) {
        if (!current_user_can('edit_page', $post_id)) {
            return;
        }
    } else {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }
    
    // Save the data
    $fields = array('address', 'phone', 'website', 'hours', 'admission', 'latitude', 'longitude');
    
    foreach ($fields as $field) {
        if (isset($_POST['petting_zoo_' . $field])) {
            update_post_meta($post_id, '_petting_zoo_' . $field, sanitize_text_field($_POST['petting_zoo_' . $field]));
        }
    }
}
add_action('save_post', 'save_petting_zoo_meta_box_data');

/**
 * AJAX handler for finding nearest zoos
 */
function find_nearest_zoos_ajax() {
    check_ajax_referer('petting_zoo_nonce', 'nonce');

    $latitude = floatval($_POST['latitude']);
    $longitude = floatval($_POST['longitude']);

    // Query for petting zoos with coordinates
    $args = array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => 10,
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_petting_zoo_latitude',
                'value' => '',
                'compare' => '!='
            ),
            array(
                'key' => '_petting_zoo_longitude',
                'value' => '',
                'compare' => '!='
            )
        )
    );

    $zoos = get_posts($args);
    $nearest_zoos = array();

    foreach ($zoos as $zoo) {
        $zoo_lat = get_post_meta($zoo->ID, '_petting_zoo_latitude', true);
        $zoo_lng = get_post_meta($zoo->ID, '_petting_zoo_longitude', true);

        if ($zoo_lat && $zoo_lng) {
            $distance = calculate_distance($latitude, $longitude, $zoo_lat, $zoo_lng);
            $nearest_zoos[] = array(
                'id' => $zoo->ID,
                'title' => $zoo->post_title,
                'distance' => $distance,
                'url' => get_permalink($zoo->ID)
            );
        }
    }

    // Sort by distance
    usort($nearest_zoos, function($a, $b) {
        return $a['distance'] <=> $b['distance'];
    });

    // Return top 5
    $nearest_zoos = array_slice($nearest_zoos, 0, 5);

    wp_send_json_success($nearest_zoos);
}
add_action('wp_ajax_find_nearest_zoos', 'find_nearest_zoos_ajax');
add_action('wp_ajax_nopriv_find_nearest_zoos', 'find_nearest_zoos_ajax');

/**
 * Calculate distance between two coordinates
 */
function calculate_distance($lat1, $lon1, $lat2, $lon2) {
    $earth_radius = 3959; // miles

    $dLat = deg2rad($lat2 - $lat1);
    $dLon = deg2rad($lon2 - $lon1);

    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));

    return $earth_radius * $c;
}

/**
 * AJAX handler for search autocomplete
 */
function zoo_search_autocomplete_ajax() {
    check_ajax_referer('petting_zoo_nonce', 'nonce');

    $query = sanitize_text_field($_POST['query']);

    $args = array(
        'post_type' => 'petting_zoo',
        'posts_per_page' => 5,
        's' => $query
    );

    $results = get_posts($args);
    $suggestions = array();

    foreach ($results as $result) {
        $suggestions[] = array(
            'title' => $result->post_title,
            'url' => get_permalink($result->ID)
        );
    }

    wp_send_json_success($suggestions);
}
add_action('wp_ajax_zoo_search_autocomplete', 'zoo_search_autocomplete_ajax');
add_action('wp_ajax_nopriv_zoo_search_autocomplete', 'zoo_search_autocomplete_ajax');



/**
 * Helper function to get city-specific content for SEO
 */
function get_city_seo_content($city_name, $state_name = '', $zoo_count = 0) {
    $location_display = $state_name ? $city_name . ', ' . $state_name : $city_name;

    $content = array(
        'intro' => "Looking for the perfect petting zoo in {$location_display}? You've come to the right place! {$city_name} offers {$zoo_count} amazing petting zoo" . ($zoo_count !== 1 ? 's' : '') . " where families can enjoy hands-on animal experiences, educational programs, and unforgettable memories.",

        'family_benefits' => "Petting zoos in {$location_display} offer exceptional family experiences that combine education, entertainment, and hands-on learning. These carefully maintained facilities provide safe environments where children can interact with gentle animals while learning about animal care, agriculture, and nature.",

        'dad_appeal' => "Petting zoos in {$location_display} offer the perfect blend of outdoor adventure, educational value, and screen-free family time that dads appreciate. Here's why these experiences are becoming the go-to weekend activity for fathers and their children:",

        'tips_intro' => "Make the most of your petting zoo adventure in {$location_display} with these helpful tips from local families and zoo experts:"
    );

    return $content;
}

/**
 * Helper function to generate FAQ content for city pages
 */
function get_city_faq_content($city_name, $state_name = '', $zoo_count = 0) {
    $location_display = $state_name ? $city_name . ', ' . $state_name : $city_name;

    $faqs = array(
        array(
            'question' => "Are there petting zoos near downtown {$city_name}?",
            'answer' => "Yes! We've found {$zoo_count} petting zoo" . ($zoo_count !== 1 ? 's' : '') . " in the {$location_display} area. Use our location filters above to find the ones closest to your preferred area of the city."
        ),
        array(
            'question' => "Do any {$city_name} petting zoos allow animal feeding?",
            'answer' => "Many petting zoos in {$location_display} offer animal feeding experiences! Look for locations with \"Animal Feeding\" in their features list, or contact individual zoos to ask about feeding schedules and opportunities."
        ),
        array(
            'question' => "What's the best age for children to visit petting zoos in {$city_name}?",
            'answer' => "Petting zoos in {$city_name} welcome children of all ages! Most locations are designed to be safe for toddlers (18 months+) while still engaging for older children and teens."
        ),
        array(
            'question' => "Is parking available at {$city_name} petting zoos?",
            'answer' => "Most petting zoos in {$location_display} offer free on-site parking. Check individual zoo pages for specific parking information and any special instructions for busy days."
        ),
        array(
            'question' => "Can we host birthday parties at petting zoos in {$city_name}?",
            'answer' => "Many locations offer birthday party packages! Look for zoos with \"Birthday Parties\" listed in their event types, or contact them directly to discuss party options and pricing."
        ),
        array(
            'question' => "What should we expect to pay for petting zoo admission in {$city_name}?",
            'answer' => "Admission prices vary by location, but most petting zoos in {$location_display} offer affordable family pricing. Check individual zoo pages for current admission rates and any available discounts."
        )
    );

    return $faqs;
}

/**
 * Add structured data for city pages
 */
function add_city_page_structured_data($city_name, $state_name = '', $faqs = array()) {
    if (empty($faqs)) {
        return;
    }

    $location_display = $state_name ? $city_name . ', ' . $state_name : $city_name;

    $structured_data = array(
        '@context' => 'https://schema.org',
        '@type' => 'FAQPage',
        'mainEntity' => array()
    );

    foreach ($faqs as $faq) {
        $structured_data['mainEntity'][] = array(
            '@type' => 'Question',
            'name' => $faq['question'],
            'acceptedAnswer' => array(
                '@type' => 'Answer',
                'text' => $faq['answer']
            )
        );
    }

    echo '<script type="application/ld+json">';
    echo wp_json_encode($structured_data, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    echo '</script>';
}

/**
 * Add admin notice to show schema validation status
 */
function petting_zoo_schema_admin_notice() {
    if (get_current_screen()->id === 'edit-petting_zoo') {
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p><strong>SEO Schema Active:</strong> All petting zoo pages now include comprehensive JSON-LD structured data for better search engine visibility. ';
        echo 'Schema includes Zoo, FAQPage, VideoObject, and BreadcrumbList markup.</p>';
        echo '</div>';
    }
}
add_action('admin_notices', 'petting_zoo_schema_admin_notice');

/**
 * Add schema validation link to admin bar for petting zoo pages
 */
function add_schema_validation_admin_bar($wp_admin_bar) {
    if (is_singular('petting_zoo')) {
        $current_url = urlencode(get_permalink());
        $validation_url = 'https://search.google.com/test/rich-results?url=' . $current_url;

        $wp_admin_bar->add_node(array(
            'id' => 'schema-validation',
            'title' => '🔍 Test Schema',
            'href' => $validation_url,
            'meta' => array(
                'target' => '_blank',
                'title' => 'Test this page\'s structured data with Google\'s Rich Results Test'
            )
        ));
    }
}
add_action('admin_bar_menu', 'add_schema_validation_admin_bar', 100);

/**
 * Add meta description for city pages
 */
function add_city_page_meta_description($city_name, $state_name = '', $zoo_count = 0) {
    $location_display = $state_name ? $city_name . ', ' . $state_name : $city_name;
    $meta_description = "Discover {$zoo_count} amazing petting zoos in {$location_display}. Find family-friendly animal experiences, educational programs, and hands-on fun for kids and adults. Compare locations, features, and plan your visit today!";

    echo '<meta name="description" content="' . esc_attr($meta_description) . '">' . "\n";
}

/**
 * Enhance city page titles for SEO
 */
function enhance_city_page_title($title, $city_name, $state_name = '', $zoo_count = 0) {
    $location_display = $state_name ? $city_name . ', ' . $state_name : $city_name;
    return "Best Petting Zoos in {$location_display} ({$zoo_count} Locations) | Family Fun & Animal Experiences";
}
