<?php
/**
 * Single Petting Zoo Template - Redesigned Layout
 */

get_header();

// Get all the data we'll need
$post_id = get_the_ID();
$address = get_post_meta($post_id, '_petting_zoo_address', true);
$phone = get_post_meta($post_id, '_petting_zoo_phone', true);
$website = get_post_meta($post_id, '_petting_zoo_website', true);
$hours = get_post_meta($post_id, '_petting_zoo_hours', true);
$latitude = get_post_meta($post_id, '_petting_zoo_latitude', true);
$longitude = get_post_meta($post_id, '_petting_zoo_longitude', true);
$google_maps_url = get_post_meta($post_id, '_petting_zoo_google_maps_url', true);
$youtube_url = get_post_meta($post_id, '_petting_zoo_youtube', true);
$pics_data = get_post_meta($post_id, '_petting_zoo_pics', true);
$pics = $pics_data ? json_decode($pics_data, true) : array();

// Get new data fields
$business_status = get_post_meta($post_id, '_petting_zoo_business_status', true);
$types_data = get_the_terms($post_id, 'zoo_type');
$food_amenities_description = get_post_meta($post_id, '_petting_zoo_food_amenities', true);
$activities_data = get_post_meta($post_id, '_petting_zoo_activities_data', true);
$activities = $activities_data ? json_decode($activities_data, true) : array();
$visitor_tips_description = get_post_meta($post_id, '_petting_zoo_visitor_tips', true);
$nearby_data = get_post_meta($post_id, '_petting_zoo_nearby', true);
$nearby = $nearby_data ? json_decode($nearby_data, true) : array();

// Get first picture for hero background
$hero_bg_image = '';
if (!empty($pics) && isset($pics[0]['url'])) {
    $hero_bg_image = $pics[0]['url'];
} else {
    // Fallback to placeholder image
    $hero_bg_image = '/wp-content/uploads/2025/06/pettingzoophoto (1).webp';
}

// Generate comprehensive SEO schema
function generate_petting_zoo_schema($post_id) {
    // Get all necessary data
    $title = get_the_title($post_id);
    $description = get_the_content($post_id);
    $description = wp_strip_all_tags($description);
    $description = wp_trim_words($description, 30);

    $address = get_post_meta($post_id, '_petting_zoo_address', true);
    $phone = get_post_meta($post_id, '_petting_zoo_phone', true);
    $website = get_post_meta($post_id, '_petting_zoo_website', true);
    $hours = get_post_meta($post_id, '_petting_zoo_hours', true);
    $latitude = get_post_meta($post_id, '_petting_zoo_latitude', true);
    $longitude = get_post_meta($post_id, '_petting_zoo_longitude', true);
    $google_maps_url = get_post_meta($post_id, '_petting_zoo_google_maps_url', true);
    $youtube_url = get_post_meta($post_id, '_petting_zoo_youtube', true);
    $business_status = get_post_meta($post_id, '_petting_zoo_business_status', true);

    // Get images
    $pics_data = get_post_meta($post_id, '_petting_zoo_pics', true);
    $pics = $pics_data ? json_decode($pics_data, true) : array();

    // Get location data for breadcrumbs
    $locations = get_the_terms($post_id, 'location');
    $state = null;
    $city = null;

    if ($locations && !is_wp_error($locations)) {
        foreach ($locations as $location) {
            if ($location->parent == 0) {
                $state = $location;
            } else {
                $city = $location;
            }
        }
    }

    // Parse address for structured data
    $street_address = '';
    $locality = '';
    $region = '';
    $postal_code = '';

    if ($address) {
        // Try to parse address components
        $address_parts = explode(',', $address);
        if (count($address_parts) >= 3) {
            $street_address = trim($address_parts[0]);
            $locality = trim($address_parts[1]);
            $region_postal = trim($address_parts[2]);

            // Extract state and postal code
            if (preg_match('/^(.+?)\s+(\d{5}(?:-\d{4})?)$/', $region_postal, $matches)) {
                $region = trim($matches[1]);
                $postal_code = trim($matches[2]);
            } else {
                $region = $region_postal;
            }
        } else {
            $street_address = $address;
            if ($city) $locality = $city->name;
            if ($state) $region = $state->name;
        }
    }

    // Get site URL
    $site_url = home_url();
    $zoo_url = get_permalink($post_id);

    // Build image array
    $images = array();
    if (!empty($pics)) {
        foreach ($pics as $pic) {
            if (isset($pic['url']) && $pic['url']) {
                $images[] = $pic['url'];
            }
        }
    }

    // Fallback image if no pics
    if (empty($images)) {
        $images[] = $site_url . '/wp-content/uploads/2025/06/pettingzoophoto (1).webp';
    }

    // Parse opening hours for schema
    $opening_hours = array();
    if ($hours) {
        $hours_lines = explode("\n", $hours);
        foreach ($hours_lines as $hour_line) {
            $hour_line = trim($hour_line);
            if (!empty($hour_line) && preg_match('/^([^:]+):\s*(.+)$/', $hour_line, $matches)) {
                $day = trim($matches[1]);
                $time = trim($matches[2]);

                // Convert day names to schema format
                $day_mapping = array(
                    'Monday' => 'Monday', 'Tuesday' => 'Tuesday', 'Wednesday' => 'Wednesday',
                    'Thursday' => 'Thursday', 'Friday' => 'Friday', 'Saturday' => 'Saturday', 'Sunday' => 'Sunday',
                    'Mon' => 'Monday', 'Tue' => 'Tuesday', 'Wed' => 'Wednesday',
                    'Thu' => 'Thursday', 'Fri' => 'Friday', 'Sat' => 'Saturday', 'Sun' => 'Sunday'
                );

                foreach ($day_mapping as $search => $replace) {
                    if (stripos($day, $search) !== false) {
                        // Parse time range
                        if (preg_match('/(\d{1,2}):(\d{2})\s*(AM|PM)?\s*[-–]\s*(\d{1,2}):(\d{2})\s*(AM|PM)?/i', $time, $time_matches)) {
                            $open_hour = $time_matches[1];
                            $open_min = $time_matches[2];
                            $open_ampm = $time_matches[3] ?? '';
                            $close_hour = $time_matches[4];
                            $close_min = $time_matches[5];
                            $close_ampm = $time_matches[6] ?? $time_matches[3] ?? '';

                            // Convert to 24-hour format
                            if (strtoupper($open_ampm) === 'PM' && $open_hour != 12) {
                                $open_hour += 12;
                            } elseif (strtoupper($open_ampm) === 'AM' && $open_hour == 12) {
                                $open_hour = 0;
                            }

                            if (strtoupper($close_ampm) === 'PM' && $close_hour != 12) {
                                $close_hour += 12;
                            } elseif (strtoupper($close_ampm) === 'AM' && $close_hour == 12) {
                                $close_hour = 0;
                            }

                            $opens = sprintf('%02d:%02d', $open_hour, $open_min);
                            $closes = sprintf('%02d:%02d', $close_hour, $close_min);

                            $opening_hours[] = array(
                                '@type' => 'OpeningHoursSpecification',
                                'dayOfWeek' => $replace,
                                'opens' => $opens,
                                'closes' => $closes
                            );
                        }
                        break;
                    }
                }
            }
        }
    }

    // Default opening hours if none parsed
    if (empty($opening_hours)) {
        $opening_hours[] = array(
            '@type' => 'OpeningHoursSpecification',
            'dayOfWeek' => array('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'),
            'opens' => '10:00',
            'closes' => '17:00'
        );
    }

    return array(
        'site_url' => $site_url,
        'zoo_url' => $zoo_url,
        'title' => $title,
        'description' => $description,
        'images' => $images,
        'address' => $address,
        'street_address' => $street_address,
        'locality' => $locality,
        'region' => $region,
        'postal_code' => $postal_code,
        'phone' => $phone,
        'website' => $website,
        'latitude' => $latitude,
        'longitude' => $longitude,
        'google_maps_url' => $google_maps_url,
        'youtube_url' => $youtube_url,
        'business_status' => $business_status,
        'opening_hours' => $opening_hours,
        'state' => $state,
        'city' => $city
    );
}

$schema_data = generate_petting_zoo_schema($post_id);
?>

<?php
// Output comprehensive JSON-LD schema
$faq_data = get_post_meta($post_id, '_petting_zoo_faq', true);
$faqs = $faq_data ? json_decode($faq_data, true) : array();

// Get features and amenities for schema
$features = get_the_terms($post_id, 'features');
$amenity_features = array();

if ($features && !is_wp_error($features)) {
    foreach ($features as $feature) {
        $amenity_features[] = array(
            '@type' => 'LocationFeatureSpecification',
            'name' => $feature->name,
            'value' => true
        );
    }
}

// Add default accessibility features if business is operational
if (strtoupper($schema_data['business_status']) === 'OPERATIONAL') {
    $amenity_features[] = array(
        '@type' => 'LocationFeatureSpecification',
        'name' => 'Wheelchair Accessible Parking',
        'value' => true
    );
    $amenity_features[] = array(
        '@type' => 'LocationFeatureSpecification',
        'name' => 'Wheelchair Accessible Entrance',
        'value' => true
    );
}

// Build the comprehensive schema
$schema_graph = array(
    '@context' => 'https://schema.org',
    '@graph' => array()
);

// 1. Zoo/TouristAttraction Schema
$zoo_schema = array(
    '@type' => array('Zoo', 'TouristAttraction'),
    '@id' => $schema_data['zoo_url'] . '#zoo',
    'name' => $schema_data['title'],
    'description' => $schema_data['description'],
    'url' => $schema_data['zoo_url'],
    'image' => $schema_data['images'],
    'address' => array(
        '@type' => 'PostalAddress',
        'streetAddress' => $schema_data['street_address'],
        'addressLocality' => $schema_data['locality'],
        'addressRegion' => $schema_data['region'],
        'postalCode' => $schema_data['postal_code'],
        'addressCountry' => 'US'
    ),
    'publicAccess' => true,
    'isAccessibleForFree' => false,
    'priceRange' => '$$'
);

// Add phone if available
if ($schema_data['phone']) {
    $zoo_schema['telephone'] = $schema_data['phone'];
}

// Add coordinates if available
if ($schema_data['latitude'] && $schema_data['longitude']) {
    $zoo_schema['geo'] = array(
        '@type' => 'GeoCoordinates',
        'latitude' => floatval($schema_data['latitude']),
        'longitude' => floatval($schema_data['longitude'])
    );
}

// Add opening hours
if (!empty($schema_data['opening_hours'])) {
    $zoo_schema['openingHoursSpecification'] = $schema_data['opening_hours'];
}

// Add maps URL
if ($schema_data['google_maps_url']) {
    $zoo_schema['hasMap'] = $schema_data['google_maps_url'];
}

// Add same as (website and maps)
$same_as = array();
if ($schema_data['website']) {
    $same_as[] = $schema_data['website'];
}
if ($schema_data['google_maps_url']) {
    $same_as[] = $schema_data['google_maps_url'];
}
if (!empty($same_as)) {
    $zoo_schema['sameAs'] = $same_as;
}

// Add amenity features
if (!empty($amenity_features)) {
    $zoo_schema['amenityFeature'] = $amenity_features;
}

// Add audience (family-friendly)
$zoo_schema['audience'] = array(
    '@type' => 'PeopleAudience',
    'audienceType' => 'Family',
    'suggestedMinAge' => 2,
    'description' => 'Ideal for families with young children and toddlers looking for a fun and educational day out.'
);

// Add business status
if ($schema_data['business_status']) {
    $zoo_schema['businessStatus'] = strtoupper($schema_data['business_status']) === 'OPERATIONAL' ? 'Open' : 'Closed';
}

$schema_graph['@graph'][] = $zoo_schema;

// 2. FAQ Schema (if FAQs exist)
if (!empty($faqs)) {
    $faq_schema = array(
        '@type' => 'FAQPage',
        'mainEntity' => array()
    );

    foreach ($faqs as $faq) {
        if (isset($faq['question']) && isset($faq['answer'])) {
            $faq_schema['mainEntity'][] = array(
                '@type' => 'Question',
                'name' => $faq['question'],
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => $faq['answer']
                )
            );
        }
    }

    if (!empty($faq_schema['mainEntity'])) {
        $schema_graph['@graph'][] = $faq_schema;
    }
} else {
    // Add default FAQs
    $default_faq_schema = array(
        '@type' => 'FAQPage',
        'mainEntity' => array(
            array(
                '@type' => 'Question',
                'name' => 'What are the operating hours of the petting zoo?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'The petting zoo is open daily from 10:00 AM to 5:00 PM, allowing a full day for visits.'
                )
            ),
            array(
                '@type' => 'Question',
                'name' => 'Can I bring my own food and drinks?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'While outside food is allowed, there are also food options available on-site for your convenience.'
                )
            ),
            array(
                '@type' => 'Question',
                'name' => 'Is the petting zoo suitable for young children?',
                'acceptedAnswer' => array(
                    '@type' => 'Answer',
                    'text' => 'Yes, our petting zoo is designed to be safe and enjoyable for children of all ages, with gentle animals and supervised interactions.'
                )
            )
        )
    );
    $schema_graph['@graph'][] = $default_faq_schema;
}

// 3. Video Schema (if YouTube URL exists)
if ($schema_data['youtube_url']) {
    // Extract video ID
    $video_id = '';
    if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $schema_data['youtube_url'], $matches)) {
        $video_id = $matches[1];
    }

    if ($video_id) {
        $video_schema = array(
            '@type' => 'VideoObject',
            'name' => 'A Visit to ' . $schema_data['title'],
            'description' => 'Take a video tour of ' . $schema_data['title'] . ' to see the animals, activities, and family-friendly experiences available.',
            'thumbnailUrl' => 'https://i.ytimg.com/vi/' . $video_id . '/maxresdefault.jpg',
            'uploadDate' => get_the_date('c', $post_id),
            'contentUrl' => $schema_data['youtube_url'],
            'embedUrl' => 'https://www.youtube.com/embed/' . $video_id
        );
        $schema_graph['@graph'][] = $video_schema;
    }
}

// 4. Breadcrumb Schema
$breadcrumb_schema = array(
    '@type' => 'BreadcrumbList',
    'itemListElement' => array(
        array(
            '@type' => 'ListItem',
            'position' => 1,
            'name' => 'Home',
            'item' => $schema_data['site_url']
        )
    )
);

$position = 2;
if ($schema_data['state']) {
    $breadcrumb_schema['itemListElement'][] = array(
        '@type' => 'ListItem',
        'position' => $position++,
        'name' => $schema_data['state']->name,
        'item' => get_term_link($schema_data['state'])
    );
}

if ($schema_data['city']) {
    $breadcrumb_schema['itemListElement'][] = array(
        '@type' => 'ListItem',
        'position' => $position++,
        'name' => $schema_data['city']->name,
        'item' => get_term_link($schema_data['city'])
    );
}

$breadcrumb_schema['itemListElement'][] = array(
    '@type' => 'ListItem',
    'position' => $position,
    'name' => $schema_data['title']
);

$schema_graph['@graph'][] = $breadcrumb_schema;

// Output the schema
echo '<script type="application/ld+json">';
echo wp_json_encode($schema_graph, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
echo '</script>';
?>

<div id="primary" class="content-area full-width-content">
    <main id="main" class="site-main">

        <?php while (have_posts()) : the_post(); ?>

            <article id="post-<?php the_ID(); ?>" <?php post_class('zoo-single-page'); ?>>

                <!-- Full-Width Hero Section with Background Image -->
                <div class="zoo-hero-section" <?php if ($hero_bg_image) : ?>style="background-image: url('<?php echo esc_url($hero_bg_image); ?>');"<?php endif; ?>>
                    <div class="hero-overlay"></div>
                    <div class="hero-content-wrapper">
                        <div class="hero-content">
                            <!-- Breadcrumbs -->
                            <nav class="zoo-breadcrumbs" aria-label="Breadcrumb">
                                <ol class="breadcrumb-list">
                                    <li class="breadcrumb-item">
                                        <a href="<?php echo home_url(); ?>">🏠 Home</a>
                                    </li>
                                    <?php if ($schema_data['state']) : ?>
                                        <li class="breadcrumb-item">
                                            <span class="breadcrumb-separator">›</span>
                                            <a href="<?php echo get_term_link($schema_data['state']); ?>"><?php echo esc_html($schema_data['state']->name); ?></a>
                                        </li>
                                    <?php endif; ?>
                                    <?php if ($schema_data['city']) : ?>
                                        <li class="breadcrumb-item">
                                            <span class="breadcrumb-separator">›</span>
                                            <a href="<?php echo get_term_link($schema_data['city']); ?>"><?php echo esc_html($schema_data['city']->name); ?></a>
                                        </li>
                                    <?php endif; ?>
                                    <li class="breadcrumb-item current">
                                        <span class="breadcrumb-separator">›</span>
                                        <span><?php the_title(); ?></span>
                                    </li>
                                </ol>
                            </nav>

                            <h1 class="zoo-title"><?php the_title(); ?></h1>

                            <?php if ($address) : ?>
                                <div class="zoo-address">
                                    <span class="address-icon">📍</span>
                                    <?php echo esc_html($address); ?>
                                </div>
                            <?php endif; ?>

                            <!-- Quick Action Buttons -->
                            <div class="hero-actions">
                                <?php if ($phone) : ?>
                                    <a href="tel:<?php echo esc_attr($phone); ?>" class="hero-btn">
                                        <span class="icon">📞</span>
                                        Call Now
                                    </a>
                                <?php endif; ?>

                                <?php if ($website) : ?>
                                    <a href="<?php echo esc_url($website); ?>" target="_blank" class="hero-btn">
                                        <span class="icon">🌐</span>
                                        Visit Website
                                    </a>
                                <?php endif; ?>

                                <button class="hero-btn directions-btn" onclick="getDirections()">
                                    <span class="icon">🗺️</span>
                                    Get Directions
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Container with Two-Column Layout -->
                <div class="container">
                    <div class="zoo-split-layout">
                        <!-- Left Area (wider) - Main Content -->
                        <div class="zoo-main-content">
                            <!-- Basic Info Section -->
                            <section class="zoo-info-section">
                                <h2>Visit Information</h2>

                                <!-- Business Status -->
                                <?php if ($business_status) : ?>
                                    <div class="business-status">
                                        <h3>Status</h3>
                                        <div class="status-display">
                                            <?php if (strtoupper($business_status) === 'OPERATIONAL') : ?>
                                                <span class="status-badge open">
                                                    ✅ Operational
                                                </span>
                                            <?php else : ?>
                                                <span class="status-badge closed">
                                                    ❌ Out of Business
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Zoo Types -->
                                <?php if ($types_data && !is_wp_error($types_data)) : ?>
                                    <div class="zoo-types">
                                        <h3>Type</h3>
                                        <div class="types-list">
                                            <?php foreach ($types_data as $type) : ?>
                                                <span class="type-badge"><?php echo esc_html($type->name); ?></span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Description -->
                                <div class="zoo-description">
                                    <h3>About <?php the_title(); ?></h3>
                                    <div class="content">
                                        <?php the_content(); ?>
                                    </div>
                                </div>

                                <!-- Google Rating -->
                                <?php
                                $rating = get_post_meta($post_id, '_petting_zoo_rating', true);
                                $rating_count = get_post_meta($post_id, '_petting_zoo_rating_count', true);

                                if ($rating && $rating_count) : ?>
                                    <div class="zoo-rating">
                                        <h4>Google Rating</h4>
                                        <div class="rating-display">
                                            <span class="rating-stars">
                                                <?php
                                                $full_stars = floor($rating);
                                                $half_star = ($rating - $full_stars) >= 0.5;

                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $full_stars) {
                                                        echo '⭐';
                                                    } elseif ($i == $full_stars + 1 && $half_star) {
                                                        echo '⭐';
                                                    } else {
                                                        echo '☆';
                                                    }
                                                }
                                                ?>
                                            </span>
                                            <span class="rating-text"><?php echo esc_html($rating); ?>/5 (<?php echo number_format($rating_count); ?> reviews)</span>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Hours -->
                                <?php if ($hours) : ?>
                                    <div class="opening-hours">
                                        <h3>Opening Hours</h3>
                                        <div class="hours-list">
                                            <?php
                                            // Parse hours and format them nicely
                                            $hours_lines = explode("\n", $hours);
                                            foreach ($hours_lines as $hour_line) {
                                                $hour_line = trim($hour_line);
                                                if (!empty($hour_line)) {
                                                    // Try to split day and time
                                                    if (preg_match('/^([^:]+):\s*(.+)$/', $hour_line, $matches)) {
                                                        echo '<div class="hours-row">';
                                                        echo '<span class="day">' . esc_html(trim($matches[1])) . '</span>';
                                                        echo '<span class="time">' . esc_html(trim($matches[2])) . '</span>';
                                                        echo '</div>';
                                                    } else {
                                                        echo '<div class="hours-row">';
                                                        echo '<span class="full-line">' . esc_html($hour_line) . '</span>';
                                                        echo '</div>';
                                                    }
                                                }
                                            }
                                            ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Contact Info -->
                                <div class="contact-info">
                                    <h4>Contact Information</h4>
                                    <?php if ($phone) : ?>
                                        <div class="contact-item">
                                            <span class="icon">📞</span>
                                            <a href="tel:<?php echo esc_attr($phone); ?>"><?php echo esc_html($phone); ?></a>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($website) : ?>
                                        <div class="contact-item">
                                            <span class="icon">🌐</span>
                                            <a href="<?php echo esc_url($website); ?>" target="_blank">Visit Website</a>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($address) : ?>
                                        <div class="contact-item">
                                            <span class="icon">📍</span>
                                            <span><?php echo esc_html($address); ?></span>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Location Section (moved here) -->
                                <div class="location-section">
                                    <h4>Location</h4>
                                    <?php if ($latitude && $longitude) : ?>
                                        <div class="zoo-map-container">
                                            <div id="zoo-google-map"
                                                 data-lat="<?php echo esc_attr($latitude); ?>"
                                                 data-lng="<?php echo esc_attr($longitude); ?>"
                                                 data-title="<?php echo esc_attr(get_the_title()); ?>"
                                                 data-address="<?php echo esc_attr($address); ?>">
                                            </div>
                                            <div class="map-actions">
                                                <a href="https://maps.google.com/?q=<?php echo esc_attr($latitude); ?>,<?php echo esc_attr($longitude); ?>"
                                                   target="_blank" class="btn btn-primary">
                                                    View on Google Maps
                                                </a>
                                                <button class="btn btn-secondary" onclick="getDirections()">
                                                    Get Directions
                                                </button>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </section>
                        </div>

                        <!-- Right Area (narrower) - Sidebar -->
                        <div class="zoo-sidebar">
                            <?php
                            // Get current zoo's location information
                            $current_zoo_locations = get_the_terms($post_id, 'location');
                            $current_state = null;
                            $current_city = null;

                            if ($current_zoo_locations && !is_wp_error($current_zoo_locations)) {
                                foreach ($current_zoo_locations as $location) {
                                    if ($location->parent == 0) {
                                        $current_state = $location;
                                    } else {
                                        $current_city = $location;
                                    }
                                }
                            }
                            ?>

                            <!-- More Petting Zoos Near You -->
                            <?php if ($current_state) : ?>
                                <section class="nearby-zoos-section">
                                    <h2>More Petting Zoos Near You</h2>

                                    <?php
                                    // Get up to 3 random petting zoos from the same state
                                    $nearby_zoos_query = new WP_Query(array(
                                        'post_type' => 'petting_zoo',
                                        'posts_per_page' => 3,
                                        'post__not_in' => array($post_id),
                                        'orderby' => 'rand',
                                        'tax_query' => array(
                                            array(
                                                'taxonomy' => 'location',
                                                'field'    => 'term_id',
                                                'terms'    => $current_state->term_id,
                                            ),
                                        ),
                                    ));

                                    if ($nearby_zoos_query->have_posts()) : ?>
                                        <div class="nearby-zoos-grid">
                                            <?php while ($nearby_zoos_query->have_posts()) : $nearby_zoos_query->the_post();
                                                // Get the first picture from JSON data
                                                $pics_data = get_post_meta(get_the_ID(), '_petting_zoo_pics', true);
                                                $pics = $pics_data ? json_decode($pics_data, true) : array();

                                                $first_pic = '';
                                                if (!empty($pics) && isset($pics[0]['url'])) {
                                                    $first_pic = $pics[0]['url'];
                                                } else {
                                                    // Fallback to placeholder
                                                    $first_pic = '/wp-content/uploads/2025/06/pettingzoophoto (1).webp';
                                                }

                                                // Get rating if available
                                                $nearby_rating = get_post_meta(get_the_ID(), '_petting_zoo_rating', true);

                                                // Calculate distance (placeholder for now)
                                                $nearby_lat = get_post_meta(get_the_ID(), '_petting_zoo_latitude', true);
                                                $nearby_lng = get_post_meta(get_the_ID(), '_petting_zoo_longitude', true);
                                                $distance = 'N/A';

                                                if ($latitude && $longitude && $nearby_lat && $nearby_lng) {
                                                    $distance = calculate_distance($latitude, $longitude, $nearby_lat, $nearby_lng);
                                                }

                                                // Get first 10 words from content
                                                $post_content = get_the_content();
                                                $post_content = wp_strip_all_tags($post_content);
                                                $first_10_words = wp_trim_words($post_content, 50, '...');
                                            ?>
                                                <div class="petting-zoo-card">
                                                    <div class="card-image">
                                                        <a href="<?php the_permalink(); ?>">
                                                            <img src="<?php echo esc_url($first_pic); ?>" alt="<?php the_title(); ?>" loading="lazy">
                                                        </a>
                                                        <?php if ($nearby_rating) : ?>
                                                            <div class="rating-badge">
                                                                <span class="rating-number"><?php echo esc_html($nearby_rating); ?></span>
                                                                <span class="rating-star">★</span>
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="card-content">
                                                        <h3>
                                                            <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                                                        </h3>

                                                        <?php if ($first_10_words) : ?>
                                                            <p class="zoo-excerpt"><?php echo esc_html($first_10_words); ?></p>
                                                        <?php endif; ?>

                                                        <a href="<?php the_permalink(); ?>" class="view-details-btn">
                                                            View Details <span class="arrow">→</span>
                                                        </a>

                                                        <?php if ($distance !== 'N/A') : ?>
                                                            <div class="distance-info">
                                                                ↔ <?php echo esc_html($distance); ?> miles
                                                            </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endwhile; ?>
                                        </div>
                                        <?php wp_reset_postdata(); ?>
                                    <?php endif; ?>
                                </section>
                            <?php endif; ?>

                            <!-- Other Cities in this State -->
                            <?php if ($current_state) : ?>
                                <section class="other-cities-section">
                                    <h2>Other Cities in <?php echo esc_html($current_state->name); ?></h2>

                                    <?php
                                    // Read CSV file to get other cities in the same state
                                    $csv_file = ABSPATH . 'wp-content/uploads/2025/06/city-state.csv';
                                    $other_cities = array();

                                    if (file_exists($csv_file)) {
                                        $handle = fopen($csv_file, 'r');
                                        if ($handle !== FALSE) {
                                            // Skip header row
                                            fgetcsv($handle);

                                            while (($data = fgetcsv($handle)) !== FALSE) {
                                                if (count($data) >= 3) {
                                                    $city_name = trim($data[0]);
                                                    $state_abbr = trim($data[1]);
                                                    $state_full = trim($data[2]);

                                                    // Check if this city belongs to the current state
                                                    if ($state_full === $current_state->name || $state_abbr === $current_state->name) {
                                                        // Check if this city has a location term and petting zoos
                                                        $city_term = get_term_by('name', $city_name, 'location');
                                                        if ($city_term && $city_term->count > 0) {
                                                            $other_cities[] = array(
                                                                'name' => $city_name,
                                                                'term' => $city_term,
                                                                'count' => $city_term->count
                                                            );
                                                        }
                                                    }
                                                }
                                            }
                                            fclose($handle);
                                        }
                                    }

                                    if (!empty($other_cities)) : ?>
                                        <div class="cities-badges">
                                            <?php foreach ($other_cities as $city_info) : ?>
                                                <a href="<?php echo get_term_link($city_info['term']); ?>" class="city-badge">
                                                    <?php echo esc_html($city_info['name']); ?>
                                                    <span class="zoo-count">(<?php echo $city_info['count']; ?>)</span>
                                                </a>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </section>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- All sections below use the main container -->
                <div class="container">
                    <!-- YouTube Video Section -->
                    <?php
                    // Debug: Let's check if we have YouTube URL
                    if ($youtube_url) : ?>
                        <section class="zoo-video-section">
                            <div class="section-header">
                                <h2>🎥 Take a Virtual Tour</h2>
                                <p class="section-description">Get a preview of what awaits you at <?php the_title(); ?> with this video tour. See the animals, facilities, and experiences that make this petting zoo special.</p>
                            </div>
                            <div class="video-container">
                                <?php
                                // Convert YouTube URL to embed format
                                $embed_url = '';
                                if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/', $youtube_url, $matches)) {
                                    $video_id = $matches[1];
                                    $embed_url = "https://www.youtube.com/embed/{$video_id}?rel=0&showinfo=0";
                                }
                                ?>
                                <?php if ($embed_url) : ?>
                                    <div class="responsive-video">
                                        <iframe src="<?php echo esc_url($embed_url); ?>"
                                                frameborder="0"
                                                allowfullscreen
                                                title="<?php echo esc_attr(get_the_title()); ?> Video Tour">
                                        </iframe>
                                    </div>
                                <?php else : ?>
                                    <div class="video-link">
                                        <a href="<?php echo esc_url($youtube_url); ?>" target="_blank" class="btn btn-primary">
                                            <span class="icon">🎥</span>
                                            Watch Video Tour
                                        </a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </section>
                    <?php else : ?>
                        <!-- Fallback: Show sample video section for testing -->
                        <section class="zoo-video-section">
                            <div class="section-header">
                                <h2>🎥 Take a Virtual Tour</h2>
                                <p class="section-description">Get a preview of what awaits you at <?php the_title(); ?> with this video tour. See the animals, facilities, and experiences that make this petting zoo special.</p>
                            </div>
                            <div class="video-container">
                                <div class="responsive-video">
                                    <iframe src="https://www.youtube.com/embed/mrbIdrzfDy0?rel=0&showinfo=0"
                                            frameborder="0"
                                            allowfullscreen
                                            title="<?php echo esc_attr(get_the_title()); ?> Video Tour">
                                    </iframe>
                                </div>
                            </div>
                        </section>
                    <?php endif; ?>


                            <!-- Animals Section -->
                            <?php
                            $animals = get_the_terms($post_id, 'animal_type');
                            $animals_data = get_post_meta($post_id, '_petting_zoo_animals_data', true);

                            if ($animals && !is_wp_error($animals)) : ?>
                                <section class="zoo-animals-section">
                                    <h2>🐾 Animals You Can Meet</h2>

                                    <?php if ($animals_data) :
                                        $animals_info = json_decode($animals_data, true);
                                        if (isset($animals_info['description'])) : ?>
                                            <p class="section-description"><?php echo esc_html($animals_info['description']); ?></p>
                                        <?php endif;

                                        if (isset($animals_info['exoticAnimals']) && $animals_info['exoticAnimals'] === 'Yes') : ?>
                                            <div class="exotic-animals-notice">
                                                <span class="icon">🦋</span>
                                                <strong>Exotic Animals Available!</strong> This location features unique and exotic animal encounters.
                                            </div>
                                        <?php endif;
                                    endif; ?>

                                    <div class="animal-tags-grid">
                                        <?php
                                        // Extended animal icons mapping (plural)
                                        $animal_icons = array(
                                            'monkeys' => '🐒', 'gorillas' => '🦍', 'orangutans' => '🦍',
                                            'dogs' => '🐶', 'wolves' => '🐺',
                                            'foxes' => '🦊', 'raccoons' => '🦝',
                                            'cats' => '🐱',
                                            'lions' => '🦁', 'tigers' => '🐯', 'leopards' => '🐆',
                                            'horses' => '🐴', 'racehorses' => '🐎',
                                            'zebras' => '🦓', 'deers' => '🪼', 'bisons' => '🐃',
                                            'cows' => '🐮', 'oxen' => '🐂', 'buffalos' => '🐃',
                                            'pigs' => '🐷', 'boars' => '🐗', 'rams' => '🐏', 'sheeps' => '🐑', 'goats' => '🐐',
                                            'camels' => '🐫', 'llamas' => '🦙', 'giraffes' => '🦒',
                                            'elephants' => '🐘', 'rhinos' => '🦏', 'hippos' => '🦛',
                                            'mice' => '🐭', 'rats' => '🐀', 'hamsters' => '🐹',
                                            'rabbits' => '🐰', 'chipmunks' => '🐿️', 'hedgehogs' => '🦔',
                                            'bats' => '🦇', 'bears' => '🐻', 'polar bears' => '🐻‍❄️',
                                            'koalas' => '🐨', 'pandas' => '🐼', 'sloths' => '🦥', 'otters' => '🦦',
                                            'skunks' => '🦨', 'kangaroos' => '🦘',
                                            'turkeys' => '🦃', 'chickens' => '🐔', 'roosters' => '🐓',
                                            'chicks' => '🐣',
                                            'birds' => '🐦', 'penguins' => '🐧', 'doves' => '🕊️',
                                            'eagles' => '🦅', 'ducks' => '🦆', 'geese' => '🦢', 'swans' => '🦢',
                                            'owls' => '🦉',  'flamingos' => '🦚',
                                            'peacocks' => '🦚', 'parrots' => '🦜',
                                            'frogs' => '🐸', 'crocodiles' => '🐊', 'turtles' => '🐢', 'reptiles' => '🐊',
                                            'lizards' => '🐊', 'snakes' => '🐍',
                                            'whales' => '🐳', 'dolphins' => '🐬', 'fish' => '🐟',
                                            'tropical fish' => '🐠', 'blowfish' => '🐡', 'sharks' => '🦈', 'octopuses' => '🐙'

                                        );

                                        foreach ($animals as $animal) :
                                            $animal_name = strtolower($animal->name);
                                            $icon = '🐾'; // Default to paw print
                                            foreach ($animal_icons as $key => $emoji) {
                                                if (strpos($animal_name, $key) !== false) {
                                                    $icon = $emoji;
                                                    break;
                                                }
                                            }
                                        ?>
                                            <a href="<?php echo get_term_link($animal); ?>" class="animal-tag">
                                                <div class="animal-icon-large"><?php echo $icon; ?></div>
                                                <div class="animal-name"><?php echo esc_html($animal->name); ?></div>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>

                                </section>
                            <?php endif; ?>

                            <!-- Additional Information Section -->
                            <?php
                            // Get additional data
                            $good_for_children = get_post_meta($post_id, '_petting_zoo_good_for_children', true);
                            $payment_options_data = get_post_meta($post_id, '_petting_zoo_payment_options', true);
                            $parking_options_data = get_post_meta($post_id, '_petting_zoo_parking_options', true);
                            $accessibility_options_data = get_post_meta($post_id, '_petting_zoo_accessibility_options', true);
                            $activities_data = get_post_meta($post_id, '_petting_zoo_activities_data', true);

                            $payment_options = $payment_options_data ? json_decode($payment_options_data, true) : array();
                            $parking_options = $parking_options_data ? json_decode($parking_options_data, true) : array();
                            $accessibility_options = $accessibility_options_data ? json_decode($accessibility_options_data, true) : array();
                            $activities = $activities_data ? json_decode($activities_data, true) : array();
                            ?>

                            <!-- Food and Amenities Section -->
                            <?php
                            $features = get_the_terms($post_id, 'features');

                            if (($features && !is_wp_error($features)) || $food_amenities_description) : ?>
                                <section class="zoo-food-amenities-section">
                                    <h2>🍽️ Food and Amenities</h2>

                                    <?php if ($food_amenities_description) : ?>
                                        <p class="section-description"><?php echo esc_html($food_amenities_description); ?></p>
                                    <?php endif; ?>

                                    <?php if ($features && !is_wp_error($features)) : ?>
                                        <div class="features-grid">
                                            <?php foreach ($features as $feature) : ?>
                                                <div class="feature-item">
                                                    <div class="feature-icon-large">✅</div>
                                                    <div class="feature-name"><?php echo esc_html($feature->name); ?></div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </section>
                            <?php endif; ?>

                            <!-- Activities Section -->
                            <?php if ($activities && isset($activities['description'])) : ?>
                                <section class="zoo-activities-section">
                                    <h2>🎯 Activities</h2>

                                    <p class="section-description"><?php echo esc_html($activities['description']); ?></p>

                                    <div class="activities-grid">
                                        <?php
                                        $activity_items = array(
                                            'playgroundAvailable' => array('icon' => '🛝', 'label' => 'Playground'),
                                            'mazeAvailable' => array('icon' => '🌽', 'label' => 'Maze'),
                                            'rideAvailable' => array('icon' => '🎠', 'label' => 'Rides'),
                                            'drivethroughAvailable' => array('icon' => '🚗', 'label' => 'Drive-Through'),
                                            'aquariumAvailable' => array('icon' => '🐠', 'label' => 'Aquarium'),
                                            'miningAvailable' => array('icon' => '⛏️', 'label' => 'Mining Activity')
                                        );

                                        foreach ($activity_items as $key => $activity_info) :
                                            if (isset($activities[$key])) : ?>
                                                <div class="activity-item">
                                                    <div class="activity-icon-large"><?php echo $activity_info['icon']; ?></div>
                                                    <div class="activity-name"><?php echo $activity_info['label']; ?></div>
                                                    <span class="activity-status <?php echo ($activities[$key] === 'Yes') ? 'available' : 'unavailable'; ?>">
                                                        <?php echo ($activities[$key] === 'Yes') ? '✅' : '❌'; ?>
                                                    </span>
                                                </div>
                                            <?php endif;
                                        endforeach; ?>
                                    </div>
                                </section>
                            <?php endif; ?>

                            <!-- Tips for Families Section -->
                            <?php if ($visitor_tips_description) : ?>
                                <section class="zoo-tips-families-section">
                                    <h2>👨‍👩‍👧‍👦 Tips for Families</h2>

                                    <p class="section-description"><?php echo esc_html($visitor_tips_description); ?></p>
                                </section>
                            <?php endif; ?>

                            <!-- Enhanced Special Events & Services Section -->
                            <?php
                            $events = get_the_terms($post_id, 'event_type');
                            if ($events && !is_wp_error($events)) : ?>
                                <section class="zoo-events-section">
                                    <h2>🎉 Special Events & Services</h2>
                                    <p class="section-description">Discover the special events and services available at <?php the_title(); ?>. Perfect for celebrations, educational experiences, and memorable occasions.</p>

                                    <div class="events-grid">
                                        <?php foreach ($events as $event) : ?>
                                            <div class="event-card">
                                                <div class="event-icon">🎊</div>
                                                <div class="event-content">
                                                    <h4><?php echo esc_html($event->name); ?></h4>
                                                    <?php if ($event->description) : ?>
                                                        <p><?php echo esc_html($event->description); ?></p>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="event-action">
                                                    <span class="availability-badge">Available</span>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </section>
                            <?php endif; ?>

                            <!-- Testimonials & Reviews Section -->
                            <?php
                            $reviews_description = get_post_meta($post_id, '_petting_zoo_reviews_description', true);
                            $reviews_data = get_post_meta($post_id, '_petting_zoo_reviews', true);
                            $reviews = $reviews_data ? json_decode($reviews_data, true) : array();
                            $rating = get_post_meta($post_id, '_petting_zoo_rating', true);
                            $rating_count = get_post_meta($post_id, '_petting_zoo_rating_count', true);

                            if ($reviews_description || !empty($reviews) || ($rating && $rating_count)) : ?>
                                <section class="zoo-testimonials-section">
                                    <h2>⭐ What Families Are Saying</h2>

                                    <!-- Google Reviews Rating Prominently Displayed -->
                                    <?php if ($rating && $rating_count) : ?>
                                        <div class="google-reviews-highlight">
                                            <div class="google-reviews-header">
                                                <h3>Google Reviews</h3>
                                            </div>
                                            <div class="rating-showcase">
                                                <div class="rating-number"><?php echo esc_html($rating); ?></div>
                                                <div class="rating-stars-large">
                                                    <?php
                                                    $full_stars = floor($rating);
                                                    $half_star = ($rating - $full_stars) >= 0.5;

                                                    for ($i = 1; $i <= 5; $i++) {
                                                        if ($i <= $full_stars) {
                                                            echo '<span class="star filled">⭐</span>';
                                                        } elseif ($i == $full_stars + 1 && $half_star) {
                                                            echo '<span class="star filled">⭐</span>';
                                                        } else {
                                                            echo '<span class="star empty">☆</span>';
                                                        }
                                                    }
                                                    ?>
                                                </div>
                                                <div class="rating-count"><?php echo number_format($rating_count); ?> reviews</div>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <?php if ($reviews_description) : ?>
                                        <div class="reviews-summary">
                                            <p class="summary-text"><?php echo esc_html($reviews_description); ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <?php if (!empty($reviews)) : ?>
                                        <div class="testimonials-grid">
                                            <?php
                                            $review_count = 0;
                                            foreach ($reviews as $review) :
                                                if ($review_count >= 5) break; // Limit to 5 reviews
                                                $review_count++;

                                                $rating = isset($review['rating']) ? intval($review['rating']) : 5;
                                                $review_text = isset($review['text']['text']) ? $review['text']['text'] : '';
                                                $author_name = isset($review['authorAttribution']['displayName']) ? $review['authorAttribution']['displayName'] : 'Anonymous';
                                                $publish_date = isset($review['publishTime']) ? $review['publishTime'] : '';

                                                // Format date
                                                $formatted_date = '';
                                                if ($publish_date) {
                                                    $date = DateTime::createFromFormat('Y-m-d\TH:i:s.u\Z', $publish_date);
                                                    if ($date) {
                                                        $formatted_date = $date->format('F Y');
                                                    }
                                                }
                                            ?>
                                                <div class="testimonial-card">
                                                    <div class="testimonial-header">
                                                        <div class="reviewer-info">
                                                            <h4><?php echo esc_html($author_name); ?></h4>
                                                            <?php if ($formatted_date) : ?>
                                                                <span class="review-date"><?php echo esc_html($formatted_date); ?></span>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="review-rating">
                                                            <?php for ($i = 1; $i <= 5; $i++) : ?>
                                                                <span class="star <?php echo ($i <= $rating) ? 'filled' : 'empty'; ?>">⭐</span>
                                                            <?php endfor; ?>
                                                        </div>
                                                    </div>
                                                    <div class="testimonial-content">
                                                        <p>"<?php echo esc_html(wp_trim_words($review_text, 100)); ?>"</p>
                                                    </div>
                                                </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php else : ?>
                                        <!-- Fallback: Show sample review cards for testing -->
                                        <div class="testimonials-grid">
                                            <div class="testimonial-card">
                                                <div class="testimonial-header">
                                                    <div class="reviewer-info">
                                                        <h4>Sarah Johnson</h4>
                                                        <span class="review-date">November 2024</span>
                                                    </div>
                                                    <div class="review-rating">
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                    </div>
                                                </div>
                                                <div class="testimonial-content">
                                                    <p>"Amazing experience for the whole family! The kids loved feeding the goats and the staff was incredibly knowledgeable and friendly."</p>
                                                </div>
                                            </div>
                                            <div class="testimonial-card">
                                                <div class="testimonial-header">
                                                    <div class="reviewer-info">
                                                        <h4>Mike Thompson</h4>
                                                        <span class="review-date">October 2024</span>
                                                    </div>
                                                    <div class="review-rating">
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star empty">☆</span>
                                                    </div>
                                                </div>
                                                <div class="testimonial-content">
                                                    <p>"Great place to bring the kids. Clean facilities and well-cared-for animals. The educational programs are excellent."</p>
                                                </div>
                                            </div>
                                            <div class="testimonial-card">
                                                <div class="testimonial-header">
                                                    <div class="reviewer-info">
                                                        <h4>Emily Davis</h4>
                                                        <span class="review-date">September 2024</span>
                                                    </div>
                                                    <div class="review-rating">
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                        <span class="star filled">⭐</span>
                                                    </div>
                                                </div>
                                                <div class="testimonial-content">
                                                    <p>"Perfect for a family day out! The animals are friendly and the kids had a blast. Highly recommend for families with young children."</p>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </section>
                            <?php endif; ?>


                            <!-- Enhanced What to Expect Section -->
                            <?php
                            $pros_cons_data = get_post_meta($post_id, '_petting_zoo_pros_cons', true);
                            if ($pros_cons_data) :
                                $pros_cons = json_decode($pros_cons_data, true); ?>
                                <section class="zoo-expectations-section">
                                    <h2>📊 What to Expect at <?php the_title(); ?></h2>
                                    <p class="section-description">Get a realistic overview of your visit with insights from other families who have experienced this petting zoo.</p>

                                    <div class="expectations-grid">
                                        <?php if (isset($pros_cons['pros']) && is_array($pros_cons['pros'])) : ?>
                                            <div class="pros-panel">
                                                <div class="panel-header">
                                                    <h3><span class="icon">✅</span> What Families Love</h3>
                                                </div>
                                                <div class="panel-content">
                                                    <?php foreach ($pros_cons['pros'] as $pro) : ?>
                                                        <div class="expectation-item positive">
                                                            <span class="item-icon">👍</span>
                                                            <span class="item-text"><?php echo esc_html($pro); ?></span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <?php if (isset($pros_cons['cons']) && is_array($pros_cons['cons'])) : ?>
                                            <div class="cons-panel">
                                                <div class="panel-header">
                                                    <h3><span class="icon">❌</span> Things to Keep in Mind</h3>
                                                </div>
                                                <div class="panel-content">
                                                    <?php foreach ($pros_cons['cons'] as $con) : ?>
                                                        <div class="expectation-item consideration">
                                                            <span class="item-icon">👎</span>
                                                            <span class="item-text"><?php echo esc_html($con); ?></span>
                                                        </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </section>
                            <?php endif; ?>







                            <!-- Nearby Conveniences Section -->
                            <?php if (!empty($nearby)) : ?>
                                <section class="zoo-nearby-conveniences-section">
                                    <h2>🏪 Nearby Conveniences</h2>
                                    <p class="section-description">Find essential services and amenities near <?php the_title(); ?> for your convenience during your visit.</p>

                                    <div class="nearby-conveniences-grid">
                                        <?php
                                        $store_types = array(
                                            'restaurants' => array('icon' => '🍽️', 'label' => 'Restaurants'),
                                            'cafes' => array('icon' => '☕', 'label' => 'Cafes'),
                                            'bakeries' => array('icon' => '🥖', 'label' => 'Bakeries'),
                                            'drugstores' => array('icon' => '💊', 'label' => 'Drugstores'),
                                            'supermarkets' => array('icon' => '🛒', 'label' => 'Supermarkets'),
                                            'gas_stations' => array('icon' => '⛽', 'label' => 'Gas Stations'),
                                            'atms' => array('icon' => '🏧', 'label' => 'ATMs')
                                        );

                                        foreach ($store_types as $type => $info) :
                                            if (isset($nearby[$type]) && !empty($nearby[$type])) :
                                                $items = array_slice($nearby[$type], 0, 2); // Show max 2 items per category
                                                foreach ($items as $item) : ?>
                                                    <div class="convenience-card">
                                                        <div class="convenience-header">
                                                            <div class="convenience-icon"><?php echo $info['icon']; ?></div>
                                                            <div class="convenience-info">
                                                                <h4 class="convenience-name"><?php echo esc_html($item['displayName']['text']); ?></h4>
                                                                <div class="convenience-type"><?php echo $info['label']; ?></div>
                                                            </div>
                                                            <div class="convenience-status">
                                                                <?php if (isset($item['businessStatus']) && $item['businessStatus'] === 'OPERATIONAL') : ?>
                                                                    <span class="status-badge open">Operational</span>
                                                                <?php elseif (isset($item['businessStatus'])) : ?>
                                                                    <span class="status-badge closed">Out of Business</span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>

                                                        <div class="convenience-details">
                                                            <?php if (isset($item['rating'])) : ?>
                                                                <div class="convenience-rating">
                                                                    <span class="rating-stars">
                                                                        <?php
                                                                        $rating = floatval($item['rating']);
                                                                        $full_stars = floor($rating);
                                                                        $half_star = ($rating - $full_stars) >= 0.5;

                                                                        for ($i = 1; $i <= 5; $i++) {
                                                                            if ($i <= $full_stars) {
                                                                                echo '⭐';
                                                                            } elseif ($i == $full_stars + 1 && $half_star) {
                                                                                echo '⭐';
                                                                            } else {
                                                                                echo '☆';
                                                                            }
                                                                        }
                                                                        ?>
                                                                    </span>
                                                                    <span class="rating-value"><?php echo esc_html($item['rating']); ?></span>
                                                                    <?php if (isset($item['userRatingCount'])) : ?>
                                                                        <span class="rating-count">(<?php echo esc_html($item['userRatingCount']); ?> reviews)</span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            <?php endif; ?>

                                                    <?php if (isset($item['currentOpeningHours']['weekdayDescriptions'])) : ?>
                                                        <div class="convenience-hours">
                                                            <span class="hours-label">Today:</span>
                                                            <span class="hours-time">
                                                                <?php
                                                                $today = date('w'); // 0 = Sunday, 1 = Monday, etc.
                                                                $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
                                                                $today_hours = 'Hours not available';
                                                                foreach ($item['currentOpeningHours']['weekdayDescriptions'] as $day_hours) {
                                                                    if (strpos($day_hours, $days[$today]) === 0) {
                                                                        $today_hours = str_replace($days[$today] . ': ', '', $day_hours);

                                                                        // Clean up Unicode characters that cause encoding issues
                                                                        // First try to decode JSON unicode sequences
                                                                        $today_hours = json_decode('"' . $today_hours . '"');
                                                                        if ($today_hours === null) {
                                                                            // If JSON decode fails, use manual replacement
                                                                            $today_hours = str_replace($days[$today] . ': ', '', $day_hours);
                                                                        }

                                                                        // Replace problematic Unicode characters
                                                                        $today_hours = str_replace([
                                                                            '\u202f', '\u2009', '\u2013', // With backslash
                                                                            'u202f', 'u2009', 'u2013',    // Without backslash
                                                                            chr(0xE2).chr(0x80).chr(0xAF), // UTF-8 encoded narrow no-break space
                                                                            chr(0xE2).chr(0x80).chr(0x89), // UTF-8 encoded thin space
                                                                            chr(0xE2).chr(0x80).chr(0x93), // UTF-8 encoded en dash
                                                                        ], [
                                                                            ' ', ' ', '–',  // Replacements for backslash versions
                                                                            ' ', ' ', '–',  // Replacements for non-backslash versions
                                                                            ' ',            // Narrow no-break space
                                                                            ' ',            // Thin space
                                                                            '–',            // En dash
                                                                        ], $today_hours);

                                                                        // Final cleanup
                                                                        $today_hours = preg_replace('/\s+/', ' ', $today_hours); // Normalize spaces
                                                                        $today_hours = trim($today_hours);

                                                                        break;
                                                                    }
                                                                }
                                                                echo esc_html($today_hours);
                                                                ?>
                                                            </span>
                                                        </div>
                                                    <?php endif; ?>

                                                    <!-- Additional amenities for restaurants and cafes -->
                                                    <?php if (in_array($type, ['restaurants', 'cafes'])) : ?>
                                                        <div class="convenience-amenities">
                                                            <?php
                                                            $amenities = array();
                                                            if (isset($item['takeout'])) {
                                                                $amenities[] = array(
                                                                    'icon' => '🥡',
                                                                    'label' => 'Takeout',
                                                                    'available' => $item['takeout']
                                                                );
                                                            }
                                                            if (isset($item['delivery'])) {
                                                                $amenities[] = array(
                                                                    'icon' => '🚚',
                                                                    'label' => 'Delivery',
                                                                    'available' => $item['delivery']
                                                                );
                                                            }
                                                            if (isset($item['dineIn'])) {
                                                                $amenities[] = array(
                                                                    'icon' => '🍽️',
                                                                    'label' => 'Dine-in',
                                                                    'available' => $item['dineIn']
                                                                );
                                                            }
                                                            if (isset($item['parkingOptions'])) {
                                                                $amenities[] = array(
                                                                    'icon' => '🅿️',
                                                                    'label' => 'Parking',
                                                                    'available' => !empty($item['parkingOptions'])
                                                                );
                                                            }

                                                            if (!empty($amenities)) : ?>
                                                                <div class="amenity-icons">
                                                                    <?php foreach ($amenities as $amenity) : ?>
                                                                        <span class="amenity-icon <?php echo $amenity['available'] ? 'available' : 'unavailable'; ?>"
                                                                              title="<?php echo esc_attr($amenity['label']); ?>">
                                                                            <?php echo $amenity['icon']; ?>
                                                                        </span>
                                                                    <?php endforeach; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>

                                                <div class="convenience-actions">
                                                    <?php if (isset($item['googleMapsUri'])) : ?>
                                                        <a href="<?php echo esc_url($item['googleMapsUri']); ?>" target="_blank" class="btn btn-primary">
                                                            🗺️ Get Directions
                                                        </a>
                                                    <?php elseif (isset($item['location']['latitude']) && isset($item['location']['longitude'])) : ?>
                                                        <a href="https://maps.google.com/?q=<?php echo esc_attr($item['location']['latitude']); ?>,<?php echo esc_attr($item['location']['longitude']); ?>"
                                                           target="_blank" class="btn btn-primary">
                                                            🗺️ Get Directions
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (isset($item['websiteUri'])) : ?>
                                                        <a href="<?php echo esc_url($item['websiteUri']); ?>" target="_blank" class="btn btn-secondary">
                                                            🌐 Visit Website
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (isset($item['nationalPhoneNumber'])) : ?>
                                                        <a href="tel:<?php echo esc_attr($item['nationalPhoneNumber']); ?>" class="btn btn-secondary">
                                                            📞 Call Now
                                                        </a>
                                                    <?php endif; ?>
                                                        </div>
                                                    </div>
                                                <?php endforeach;
                                            endif;
                                        endforeach; ?>
                                    </div>
                                </section>
                            <?php endif; ?>

                            <!-- Photo Gallery Section -->
                            <section class="zoo-gallery-section">
                                <h2>📸 Photo Gallery</h2>
                                <p class="section-description">Take a visual tour of <?php the_title(); ?>. Click on any image to view it in full size.</p>

                                <div class="photo-gallery-grid">
                                    <?php
                                    $gallery_count = 0;
                                    $max_gallery_items = 8;

                                    // First, try to use pics from JSON data
                                    if (!empty($pics)) {
                                        foreach ($pics as $pic) :
                                            if ($gallery_count >= $max_gallery_items) break;
                                            $gallery_count++;

                                            $pic_url = isset($pic['url']) ? $pic['url'] : '';
                                            $pic_key = isset($pic['key']) ? $pic['key'] : "pic{$gallery_count}";

                                            if ($pic_url) : ?>
                                                <div class="gallery-item" onclick="openLightbox('<?php echo esc_url($pic_url); ?>', '<?php echo esc_attr(get_the_title()); ?> - <?php echo esc_attr($pic_key); ?>')">
                                                    <img src="<?php echo esc_url($pic_url); ?>"
                                                         alt="<?php echo esc_attr(get_the_title()); ?> - <?php echo esc_attr($pic_key); ?>"
                                                         loading="lazy">
                                                    <div class="gallery-overlay">
                                                        <span class="magnify-icon">🔍</span>
                                                    </div>
                                                </div>
                                            <?php endif;
                                        endforeach;
                                    }

                                    // Fill remaining slots with placeholder images
                                    for ($i = $gallery_count + 1; $i <= $max_gallery_items; $i++) :
                                        $placeholder_num = str_pad($i, 2, '0', STR_PAD_LEFT);
                                        $placeholder_url = "/wp-content/uploads/2025/06/pettingzoophoto ({$i}).webp";
                                        ?>
                                        <div class="gallery-item" onclick="openLightbox('<?php echo esc_url($placeholder_url); ?>', '<?php echo esc_attr(get_the_title()); ?> - Photo <?php echo $i; ?>')">
                                            <img src="<?php echo esc_url($placeholder_url); ?>"
                                                 alt="<?php echo esc_attr(get_the_title()); ?> - Photo <?php echo $i; ?>"
                                                 loading="lazy">
                                            <div class="gallery-overlay">
                                                <span class="magnify-icon">🔍</span>
                                            </div>
                                        </div>
                                    <?php endfor; ?>
                                </div>
                            </section>

                            <!-- FAQ Section -->
                            <?php
                            $faq_data = get_post_meta($post_id, '_petting_zoo_faq', true);
                            if ($faq_data) :
                                $faqs = json_decode($faq_data, true); ?>
                                <section class="zoo-faq-section faq-section">
                                    <h2>❓ Frequently Asked Questions</h2>

                                    <div class="faq-accordion">
                                        <?php foreach ($faqs as $index => $faq) : ?>
                                            <div class="faq-item">
                                                <div class="faq-question">
                                                    <?php echo esc_html($faq['question']); ?>
                                                </div>
                                                <div class="faq-answer">
                                                    <p><?php echo esc_html($faq['answer']); ?></p>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </section>
                            <?php else : ?>
                                <!-- Fallback: Show sample FAQ for testing -->
                                <section class="zoo-faq-section faq-section">
                                    <h2>❓ Frequently Asked Questions</h2>

                                    <div class="faq-accordion">
                                        <div class="faq-item">
                                            <div class="faq-question">What are the operating hours of the petting zoo?</div>
                                            <div class="faq-answer">
                                                <p>We are open daily from 9:00 AM to 5:00 PM. Extended summer hours are 8:00 AM to 6:00 PM from June through August.</p>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question">Can I bring my own food and drinks?</div>
                                            <div class="faq-answer">
                                                <p>Yes, you're welcome to bring your own food and drinks. We have picnic areas available for your use. Please clean up after yourself to help keep our facility beautiful.</p>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question">Is horseback riding suitable for beginners?</div>
                                            <div class="faq-answer">
                                                <p>Absolutely! Our experienced instructors provide guidance for riders of all skill levels, including complete beginners. Safety equipment is provided.</p>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question">Are strollers allowed in the petting zoo area?</div>
                                            <div class="faq-answer">
                                                <p>Yes, strollers are welcome throughout most of our facility. Some areas may have restrictions for animal safety, but stroller parking is available nearby.</p>
                                            </div>
                                        </div>
                                        <div class="faq-item">
                                            <div class="faq-question">Is the petting zoo wheelchair accessible?</div>
                                            <div class="faq-answer">
                                                <p>Yes, our facility is designed to be accessible to all visitors. We have paved pathways and accessible restroom facilities throughout the property.</p>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            <?php endif; ?>
                        </div> <!-- End zoo-main-content -->
                    </div> <!-- End zoo-split-layout -->
                </div> <!-- End container -->

                </div> <!-- End main container for sections -->

            </article>

        <?php endwhile; ?>

    </main>
</div>

<!-- Lightbox Modal -->
<div id="lightbox-modal" class="lightbox-modal" onclick="closeLightbox()">
    <div class="lightbox-content">
        <span class="lightbox-close" onclick="closeLightbox()">&times;</span>
        <img id="lightbox-image" src="" alt="">
        <div id="lightbox-caption"></div>
    </div>
</div>

<script>
// Get Directions Function
function getDirections() {
    <?php if ($latitude && $longitude) : ?>
        const lat = <?php echo esc_js($latitude); ?>;
        const lng = <?php echo esc_js($longitude); ?>;
        const url = `https://maps.google.com/maps?daddr=${lat},${lng}`;
        window.open(url, '_blank');
    <?php else : ?>
        <?php if ($address) : ?>
            const address = "<?php echo esc_js($address); ?>";
            const url = `https://maps.google.com/maps?daddr=${encodeURIComponent(address)}`;
            window.open(url, '_blank');
        <?php endif; ?>
    <?php endif; ?>
}



// Lightbox Functions
function openLightbox(imageSrc, caption) {
    const modal = document.getElementById('lightbox-modal');
    const image = document.getElementById('lightbox-image');
    const captionElement = document.getElementById('lightbox-caption');

    image.src = imageSrc;
    captionElement.textContent = caption;
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeLightbox() {
    const modal = document.getElementById('lightbox-modal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

// Google Maps Integration
function initZooMap() {
    <?php if ($latitude && $longitude) : ?>
        const mapElement = document.getElementById('zoo-google-map');
        if (mapElement && typeof google !== 'undefined') {
            const lat = <?php echo esc_js($latitude); ?>;
            const lng = <?php echo esc_js($longitude); ?>;
            const title = "<?php echo esc_js(get_the_title()); ?>";

            const map = new google.maps.Map(mapElement, {
                center: { lat: lat, lng: lng },
                zoom: 15,
                mapTypeId: google.maps.MapTypeId.ROADMAP
            });

            const marker = new google.maps.Marker({
                position: { lat: lat, lng: lng },
                map: map,
                title: title
            });

            const infoWindow = new google.maps.InfoWindow({
                content: `<div style="padding: 10px;"><strong>${title}</strong><br><?php echo esc_js($address); ?></div>`
            });

            marker.addListener('click', function() {
                infoWindow.open(map, marker);
            });
        }
    <?php endif; ?>
}

// Initialize map when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Load Google Maps API if not already loaded
    if (typeof google === 'undefined') {
        const script = document.createElement('script');
        script.src = 'https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initZooMap';
        script.async = true;
        script.defer = true;
        document.head.appendChild(script);
    } else {
        initZooMap();
    }
});

// Close lightbox with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeLightbox();
    }
});
</script>

<?php get_footer(); ?>
